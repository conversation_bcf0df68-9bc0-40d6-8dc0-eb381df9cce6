@extends('layouts.app')

@section('header')
    <div class="flex justify-between items-center">
        <div>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-rocket mr-2 text-clickup-purple"></i>
                Taqnyat Product Management Dashboard
            </h2>
            <p class="text-sm text-gray-600 mt-1">
                Centralized tracking for all 6 Taqnyat product development initiatives
            </p>
        </div>
        <div class="text-right">
            <div class="text-2xl font-bold text-clickup-purple">{{ $overallStats['completion_rate'] }}%</div>
            <div class="text-sm text-gray-500">Overall Completion</div>
        </div>
    </div>
@endsection

@section('content')
    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tasks text-clickup-purple text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $overallStats['total_tasks'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $overallStats['completed_tasks'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-spinner text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $overallStats['in_progress_tasks'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-gray-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">To Do</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $overallStats['todo_tasks'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Cards Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        @foreach($sortedProducts as $productName => $metrics)
            @if($metrics)
                <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                    <div class="px-6 py-5">
                        <!-- Product Header -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    @php
                                        $iconClass = match($productName) {
                                            'LiveChat [Web]' => 'fas fa-comments text-blue-500',
                                            'LiveChat [Mobile]' => 'fas fa-mobile-alt text-green-500',
                                            'Chatbot' => 'fas fa-robot text-purple-500',
                                            'Email' => 'fas fa-envelope text-red-500',
                                            'Chatbot AI' => 'fas fa-brain text-indigo-500',
                                            'Call Bot' => 'fas fa-phone text-orange-500',
                                            default => 'fas fa-cube text-gray-500'
                                        };
                                    @endphp
                                    <i class="{{ $iconClass }} text-2xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $productName }}</h3>
                                    <p class="text-sm text-gray-500">{{ $metrics['total_lists'] }} lists • {{ $metrics['total_tasks'] }} tasks</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold 
                                    @if($metrics['completion_rate'] >= 80) text-green-600
                                    @elseif($metrics['completion_rate'] >= 60) text-yellow-600
                                    @elseif($metrics['completion_rate'] >= 40) text-orange-600
                                    @else text-red-600
                                    @endif">
                                    {{ $metrics['completion_rate'] }}%
                                </div>
                                <div class="text-xs text-gray-500">Completion</div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Progress</span>
                                <span>{{ $metrics['completed_tasks'] }} / {{ $metrics['total_tasks'] }} tasks</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full transition-all duration-300
                                    @if($metrics['completion_rate'] >= 80) bg-green-500
                                    @elseif($metrics['completion_rate'] >= 60) bg-yellow-500
                                    @elseif($metrics['completion_rate'] >= 40) bg-orange-500
                                    @else bg-red-500
                                    @endif" 
                                    style="width: {{ $metrics['completion_rate'] }}%">
                                </div>
                            </div>
                        </div>

                        <!-- Task Status Breakdown -->
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-green-600">{{ $metrics['completed_tasks'] }}</div>
                                <div class="text-xs text-gray-500">Completed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-blue-600">{{ $metrics['in_progress_tasks'] }}</div>
                                <div class="text-xs text-gray-500">In Progress</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-gray-600">{{ $metrics['todo_tasks'] }}</div>
                                <div class="text-xs text-gray-500">To Do</div>
                            </div>
                        </div>

                        <!-- Health Score -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm font-medium text-gray-700">Health Score</span>
                            <div class="flex items-center">
                                <div class="text-sm font-bold 
                                    @if($metrics['health_score'] >= 80) text-green-600
                                    @elseif($metrics['health_score'] >= 60) text-yellow-600
                                    @else text-red-600
                                    @endif">
                                    {{ $metrics['health_score'] }}/100
                                </div>
                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-1">
                                    <div class="h-1 rounded-full 
                                        @if($metrics['health_score'] >= 80) bg-green-500
                                        @elseif($metrics['health_score'] >= 60) bg-yellow-500
                                        @else bg-red-500
                                        @endif" 
                                        style="width: {{ $metrics['health_score'] }}%">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        @if($metrics['recent_tasks']->count() > 0)
                            <div class="border-t pt-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-clock mr-1"></i>
                                    Recent Activity ({{ $metrics['recent_tasks']->count() }} updates this week)
                                </h4>
                                <div class="space-y-1">
                                    @foreach($metrics['recent_tasks']->take(3) as $task)
                                        <div class="text-xs text-gray-600 truncate">
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium
                                                @if(($task->status['status'] ?? '') === 'complete') bg-green-100 text-green-800
                                                @elseif(str_contains($task->status['status'] ?? '', 'progress')) bg-blue-100 text-blue-800
                                                @else bg-gray-100 text-gray-800
                                                @endif">
                                                {{ ucfirst($task->status['status'] ?? 'Unknown') }}
                                            </span>
                                            <span class="ml-1">{{ Str::limit($task->name, 30) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <div class="border-t pt-4 text-center">
                                <div class="text-xs text-gray-500">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    No recent activity this week
                                </div>
                            </div>
                        @endif

                        <!-- Action Button -->
                        <div class="mt-4 pt-4 border-t">
                            <a href="{{ route('taqnyat.product-detail', $metrics['folder']->id) }}" 
                               class="w-full bg-clickup-purple hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium transition duration-150 ease-in-out block">
                                <i class="fas fa-eye mr-2"></i>
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    </div>

    <!-- Overall Progress Chart -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-bar mr-2"></i>
                Product Completion Overview
            </h3>
            <div class="relative h-64">
                <canvas id="productCompletionChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Product Completion Chart
        const ctx = document.getElementById('productCompletionChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {!! json_encode($sortedProducts->keys()) !!},
                datasets: [{
                    label: 'Completion Rate (%)',
                    data: {!! json_encode($sortedProducts->pluck('completion_rate')->values()) !!},
                    backgroundColor: [
                        '#3B82F6', // Blue for LiveChat Web
                        '#10B981', // Green for LiveChat Mobile
                        '#8B5CF6', // Purple for Chatbot
                        '#EF4444', // Red for Email
                        '#6366F1', // Indigo for Chatbot AI
                        '#F59E0B'  // Orange for Call Bot
                    ],
                    borderColor: [
                        '#2563EB',
                        '#059669',
                        '#7C3AED',
                        '#DC2626',
                        '#4F46E5',
                        '#D97706'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + '% completed';
                            }
                        }
                    }
                }
            }
        });
    </script>
@endsection
