@extends('layouts.app')

@section('header')
    <div class="flex justify-between items-center">
        <div>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-chart-line mr-2 text-clickup-purple"></i>
                Taqnyat Performance Reports
            </h2>
            <p class="text-sm text-gray-600 mt-1">
                Comprehensive analytics and insights for all 6 Taqnyat product development initiatives
            </p>
        </div>
        <div class="flex space-x-3">
            <!-- Time Period Filter -->
            <select id="periodFilter" onchange="updatePeriod()" class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                <option value="7" {{ $period == '7' ? 'selected' : '' }}>Last 7 days</option>
                <option value="30" {{ $period == '30' ? 'selected' : '' }}>Last 30 days</option>
                <option value="60" {{ $period == '60' ? 'selected' : '' }}>Last 60 days</option>
                <option value="90" {{ $period == '90' ? 'selected' : '' }}>Last 90 days</option>
            </select>
            
            <!-- Export Button -->
            <button onclick="exportTasks()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                <i class="fas fa-download mr-2"></i>
                Export CSV
            </button>
        </div>
    </div>
@endsection

@section('content')
    <!-- Performance Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        @php
            $totalTasks = collect($analytics)->sum('total_tasks');
            $totalCompleted = collect($analytics)->sum('completed_tasks');
            $totalInProgress = collect($analytics)->sum('in_progress_tasks');
            $totalBlocked = collect($analytics)->sum('blocked_tasks');
            $overallCompletionRate = $totalTasks > 0 ? round(($totalCompleted / $totalTasks) * 100, 1) : 0;
        @endphp

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tasks text-clickup-purple text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $totalTasks }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $totalCompleted }} ({{ $overallCompletionRate }}%)</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-spinner text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $totalInProgress }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Blocked/Issues</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $totalBlocked }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completion Trends Chart -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-line mr-2"></i>
                Task Completion Trends (Last {{ $period }} days)
            </h3>
            <div class="relative h-80">
                <canvas id="completionTrendsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Product Performance Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        @foreach($analytics as $productName => $metrics)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-6 py-5">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            @php
                                $iconClass = match($productName) {
                                    'LiveChat [Web]' => 'fas fa-comments text-blue-500',
                                    'LiveChat [Mobile]' => 'fas fa-mobile-alt text-green-500',
                                    'Chatbot' => 'fas fa-robot text-purple-500',
                                    'Email' => 'fas fa-envelope text-red-500',
                                    'Chatbot AI' => 'fas fa-brain text-indigo-500',
                                    'Call Bot' => 'fas fa-phone text-orange-500',
                                    default => 'fas fa-cube text-gray-500'
                                };
                            @endphp
                            <i class="{{ $iconClass }} text-2xl mr-3"></i>
                            <div>
                                <h4 class="text-lg font-medium text-gray-900">{{ $productName }}</h4>
                                <p class="text-sm text-gray-500">{{ $metrics['total_tasks'] }} total tasks</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold 
                                @if($metrics['completion_rate'] >= 80) text-green-600
                                @elseif($metrics['completion_rate'] >= 60) text-yellow-600
                                @else text-red-600
                                @endif">
                                {{ $metrics['completion_rate'] }}%
                            </div>
                            <div class="text-xs text-gray-500">Completion</div>
                        </div>
                    </div>

                    <!-- Metrics Grid -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-gray-50 rounded">
                            <div class="text-lg font-semibold text-green-600">{{ $metrics['completed_tasks'] }}</div>
                            <div class="text-xs text-gray-500">Completed</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded">
                            <div class="text-lg font-semibold text-blue-600">{{ $metrics['in_progress_tasks'] }}</div>
                            <div class="text-xs text-gray-500">In Progress</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded">
                            <div class="text-lg font-semibold text-purple-600">{{ $metrics['velocity'] }}</div>
                            <div class="text-xs text-gray-500">Velocity ({{ $period }}d)</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded">
                            <div class="text-lg font-semibold text-orange-600">{{ $metrics['avg_completion_time'] }}</div>
                            <div class="text-xs text-gray-500">Avg Days</div>
                        </div>
                    </div>

                    <!-- Bottlenecks -->
                    @if(isset($bottlenecks[$productName]['long_running_tasks']) && count($bottlenecks[$productName]['long_running_tasks']) > 0)
                        <div class="border-t pt-3">
                            <h5 class="text-sm font-medium text-red-600 mb-2">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Long-Running Tasks
                            </h5>
                            @foreach(array_slice($bottlenecks[$productName]['long_running_tasks'], 0, 2) as $task)
                                <div class="text-xs text-gray-600 mb-1">
                                    <span class="font-medium">{{ Str::limit($task['name'], 30) }}</span>
                                    <span class="text-red-500">({{ $task['days_since_update'] }} days)</span>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    <!-- View Details Button -->
                    <div class="mt-4 pt-3 border-t">
                        <button onclick="showProductTasks('{{ $productName }}')" 
                                class="w-full bg-clickup-purple hover:bg-purple-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            <i class="fas fa-list mr-2"></i>
                            View All Tasks
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Velocity Comparison Chart -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-tachometer-alt mr-2"></i>
                Product Velocity Comparison (Last {{ $period }} days)
            </h3>
            <div class="relative h-64">
                <canvas id="velocityChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Task Details Modal -->
    <div id="taskModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-screen overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Task Details</h3>
                <button onclick="closeTaskModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Task Filters -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <input type="text" id="taskSearch" placeholder="Search tasks..." 
                           class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                    
                    <select id="statusFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                        <option value="">All Statuses</option>
                    </select>
                    
                    <select id="priorityFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                        <option value="">All Priorities</option>
                    </select>
                    
                    <select id="assigneeFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                        <option value="">All Assignees</option>
                    </select>
                    
                    <input type="date" id="dateFromFilter" class="border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple text-sm">
                    
                    <button onclick="applyTaskFilters()" class="bg-clickup-purple hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-filter mr-1"></i>
                        Filter
                    </button>
                </div>
            </div>
            
            <div class="px-6 py-4 max-h-96 overflow-y-auto" id="taskList">
                <!-- Task list will be populated here -->
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
                <div id="taskPagination" class="flex space-x-2">
                    <!-- Pagination will be populated here -->
                </div>
                <button onclick="exportFilteredTasks()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Filtered
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentProduct = '';
        let currentPage = 1;
        let currentFilters = {};

        // Completion Trends Chart
        const trendsCtx = document.getElementById('completionTrendsChart').getContext('2d');
        const trendsData = {!! json_encode($completionTrends) !!};
        
        const datasets = Object.keys(trendsData).map((product, index) => {
            const colors = ['#3B82F6', '#10B981', '#8B5CF6', '#EF4444', '#6366F1', '#F59E0B'];
            return {
                label: product,
                data: trendsData[product].map(item => item.completed),
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                tension: 0.4
            };
        });

        new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: trendsData[Object.keys(trendsData)[0]]?.map(item => item.date) || [],
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Velocity Chart
        const velocityCtx = document.getElementById('velocityChart').getContext('2d');
        const velocityData = {!! json_encode($velocityMetrics) !!};
        
        new Chart(velocityCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(velocityData),
                datasets: [{
                    label: 'Tasks Completed',
                    data: Object.values(velocityData).map(v => v.completed),
                    backgroundColor: '#10B981'
                }, {
                    label: 'Tasks Created',
                    data: Object.values(velocityData).map(v => v.created),
                    backgroundColor: '#3B82F6'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function updatePeriod() {
            const period = document.getElementById('periodFilter').value;
            window.location.href = `{{ route('taqnyat.reports') }}?period=${period}`;
        }

        function exportTasks() {
            window.location.href = `{{ route('taqnyat.reports.export') }}?period={{ $period }}`;
        }

        function showProductTasks(product) {
            currentProduct = product;
            document.getElementById('modalTitle').textContent = `${product} - Task Details`;
            document.getElementById('taskModal').classList.remove('hidden');
            document.getElementById('taskModal').classList.add('flex');
            loadTasks();
        }

        function closeTaskModal() {
            document.getElementById('taskModal').classList.add('hidden');
            document.getElementById('taskModal').classList.remove('flex');
        }

        function loadTasks(page = 1) {
            currentPage = page;
            const params = new URLSearchParams({
                product: currentProduct,
                page: page,
                ...currentFilters
            });

            fetch(`{{ route('taqnyat.reports.tasks') }}?${params}`)
                .then(response => response.json())
                .then(data => {
                    displayTasks(data.tasks);
                    displayPagination(data.pagination);
                })
                .catch(error => console.error('Error loading tasks:', error));
        }

        function displayTasks(tasks) {
            const taskList = document.getElementById('taskList');
            if (tasks.length === 0) {
                taskList.innerHTML = '<div class="text-center py-8 text-gray-500">No tasks found matching the current filters.</div>';
                return;
            }

            taskList.innerHTML = tasks.map(task => `
                <div class="border-b border-gray-200 py-4">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status?.status)}">
                                    ${task.status?.status || 'Unknown'}
                                </span>
                                ${task.priority?.priority ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority.priority)}">
                                    <i class="fas fa-flag mr-1"></i>${task.priority.priority}
                                </span>` : ''}
                                ${task.tags && task.tags.length > 0 ? task.tags.map(tag => `<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getTagColor(tag.name || tag)}">
                                    <i class="fas fa-tag mr-1"></i>${tag.name || tag}
                                </span>`).join(' ') : ''}
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">
                                <a href="${task.url}" target="_blank" class="hover:text-clickup-purple">
                                    ${task.name}
                                </a>
                            </h4>
                            ${task.description ? `<p class="text-sm text-gray-600 mb-3">${task.description.substring(0, 150)}${task.description.length > 150 ? '...' : ''}</p>` : ''}
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-list mr-1"></i>${task.list?.name || 'Unknown List'}</span>
                                ${task.assignees?.[0]?.username ? `<span><i class="fas fa-user mr-1"></i>${task.assignees[0].username}</span>` : ''}
                                ${task.due_date ? `<span><i class="fas fa-calendar mr-1"></i>${new Date(task.due_date).toLocaleDateString()}</span>` : ''}
                            </div>
                        </div>
                        <div class="ml-4 text-right text-sm text-gray-500">
                            ${task.date_updated ? `Updated ${timeAgo(task.date_updated)}` : 'No update date'}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function displayPagination(pagination) {
            const paginationDiv = document.getElementById('taskPagination');
            let paginationHtml = '';

            if (pagination.current_page > 1) {
                paginationHtml += `<button onclick="loadTasks(${pagination.current_page - 1})" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Previous</button>`;
            }

            for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.last_page, pagination.current_page + 2); i++) {
                const isActive = i === pagination.current_page;
                paginationHtml += `<button onclick="loadTasks(${i})" class="px-3 py-1 border ${isActive ? 'border-clickup-purple bg-clickup-purple text-white' : 'border-gray-300 hover:bg-gray-50'} rounded text-sm">${i}</button>`;
            }

            if (pagination.current_page < pagination.last_page) {
                paginationHtml += `<button onclick="loadTasks(${pagination.current_page + 1})" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Next</button>`;
            }

            paginationDiv.innerHTML = paginationHtml;
        }

        function applyTaskFilters() {
            currentFilters = {
                search: document.getElementById('taskSearch').value,
                status: document.getElementById('statusFilter').value,
                priority: document.getElementById('priorityFilter').value,
                assignee: document.getElementById('assigneeFilter').value,
                date_from: document.getElementById('dateFromFilter').value
            };
            loadTasks(1);
        }

        function exportFilteredTasks() {
            const params = new URLSearchParams({
                product: currentProduct,
                ...currentFilters
            });
            window.location.href = `{{ route('taqnyat.reports.export') }}?${params}`;
        }

        function getStatusColor(status) {
            switch(status) {
                case 'complete': return 'bg-green-100 text-green-800';
                case 'in progress': return 'bg-blue-100 text-blue-800';
                case 'to do': return 'bg-gray-100 text-gray-800';
                default: return 'bg-yellow-100 text-yellow-800';
            }
        }

        function getPriorityColor(priority) {
            switch(priority) {
                case 'urgent': return 'bg-red-100 text-red-800';
                case 'high': return 'bg-orange-100 text-orange-800';
                case 'normal': return 'bg-blue-100 text-blue-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getTagColor(tagName) {
            const colors = [
                'bg-purple-100 text-purple-800',
                'bg-blue-100 text-blue-800',
                'bg-green-100 text-green-800',
                'bg-yellow-100 text-yellow-800',
                'bg-pink-100 text-pink-800',
                'bg-indigo-100 text-indigo-800',
                'bg-red-100 text-red-800',
                'bg-orange-100 text-orange-800'
            ];

            // Use a simple hash function to consistently assign colors
            let hash = 0;
            for (let i = 0; i < tagName.length; i++) {
                hash = ((hash << 5) - hash + tagName.charCodeAt(i)) & 0xffffffff;
            }
            return colors[Math.abs(hash) % colors.length];
        }

        function timeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) return 'just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
            return `${Math.floor(diffInSeconds / 86400)} days ago`;
        }
    </script>
@endsection
