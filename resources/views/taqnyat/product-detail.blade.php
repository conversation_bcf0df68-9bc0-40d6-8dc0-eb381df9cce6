@extends('layouts.app')

@section('header')
    <div class="flex justify-between items-center">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <a href="{{ route('taqnyat.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-rocket mr-1"></i>
                            Taqnyat Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mr-4"></i>
                            <span class="text-gray-500">{{ $folder->name }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight mt-2">
                @php
                    $iconClass = match($folder->name) {
                        'LiveChat [Web]' => 'fas fa-comments text-blue-500',
                        'LiveChat [Mobile]' => 'fas fa-mobile-alt text-green-500',
                        'Chatbot' => 'fas fa-robot text-purple-500',
                        'Email' => 'fas fa-envelope text-red-500',
                        'Chatbot AI' => 'fas fa-brain text-indigo-500',
                        'Call Bot' => 'fas fa-phone text-orange-500',
                        default => 'fas fa-cube text-gray-500'
                    };
                @endphp
                <i class="{{ $iconClass }} mr-2"></i>
                {{ $folder->name }} - Product Details
            </h2>
        </div>
        <div class="text-right">
            <a href="{{ route('taqnyat.dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
@endsection

@section('content')
    <!-- Product Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-list text-clickup-purple text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Lists</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $folder->lists->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tasks text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Tasks</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $metrics['total_tasks'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ $metrics['status_breakdown']['complete'] ?? 0 }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-percentage text-orange-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @php
                                    $completionRate = $metrics['total_tasks'] > 0 
                                        ? round((($metrics['status_breakdown']['complete'] ?? 0) / $metrics['total_tasks']) * 100, 1) 
                                        : 0;
                                @endphp
                                {{ $completionRate }}%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Status Distribution Chart -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-chart-pie mr-2"></i>
                    Task Status Distribution
                </h3>
                <div class="relative h-64">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Priority Distribution Chart -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Priority Distribution
                </h3>
                <div class="relative h-64">
                    <canvas id="priorityChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Lists and Tasks Breakdown -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-layer-group mr-2"></i>
                Lists & Tasks Breakdown
            </h3>
            
            @if($folder->lists->count() > 0)
                <div class="space-y-6">
                    @foreach($folder->lists as $list)
                        @php
                            $listTasks = $list->tasks;
                            $listCompletedTasks = $listTasks->filter(function($task) {
                                return ($task->status['status'] ?? '') === 'complete';
                            })->count();
                            $listCompletionRate = $listTasks->count() > 0 
                                ? round(($listCompletedTasks / $listTasks->count()) * 100, 1) 
                                : 0;
                        @endphp
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="text-md font-medium text-gray-900">{{ $list->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $listTasks->count() }} tasks</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold 
                                        @if($listCompletionRate >= 80) text-green-600
                                        @elseif($listCompletionRate >= 60) text-yellow-600
                                        @else text-red-600
                                        @endif">
                                        {{ $listCompletionRate }}%
                                    </div>
                                    <div class="text-xs text-gray-500">Complete</div>
                                </div>
                            </div>
                            
                            <!-- Progress Bar -->
                            <div class="mb-3">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $listCompletedTasks }} / {{ $listTasks->count() }} tasks</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full 
                                        @if($listCompletionRate >= 80) bg-green-500
                                        @elseif($listCompletionRate >= 60) bg-yellow-500
                                        @else bg-red-500
                                        @endif" 
                                        style="width: {{ $listCompletionRate }}%">
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Tasks -->
                            @if($listTasks->count() > 0)
                                <div class="space-y-2">
                                    <h5 class="text-sm font-medium text-gray-700">Recent Tasks:</h5>
                                    @foreach($listTasks->sortByDesc('date_updated')->take(3) as $task)
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex items-center space-x-2">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                                    @if(($task->status['status'] ?? '') === 'complete') bg-green-100 text-green-800
                                                    @elseif(str_contains($task->status['status'] ?? '', 'progress')) bg-blue-100 text-blue-800
                                                    @else bg-gray-100 text-gray-800
                                                    @endif">
                                                    {{ ucfirst($task->status['status'] ?? 'Unknown') }}
                                                </span>
                                                <span class="text-gray-900">{{ Str::limit($task->name, 40) }}</span>
                                            </div>
                                            <span class="text-gray-500 text-xs">
                                                {{ $task->date_updated ? $task->date_updated->diffForHumans() : 'No date' }}
                                            </span>
                                        </div>
                                    @endforeach
                                    
                                    @if($listTasks->count() > 3)
                                        <div class="text-xs text-gray-500 text-center pt-2">
                                            ... and {{ $listTasks->count() - 3 }} more tasks
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">No Lists Found</h3>
                    <p class="text-gray-600">This product folder doesn't have any lists yet.</p>
                </div>
            @endif
        </div>
    </div>

    <script>
        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode(array_keys($metrics['status_breakdown']->toArray())) !!},
                datasets: [{
                    data: {!! json_encode(array_values($metrics['status_breakdown']->toArray())) !!},
                    backgroundColor: [
                        '#10B981', // Green for complete
                        '#3B82F6', // Blue for in progress
                        '#6B7280', // Gray for to do
                        '#F59E0B', // Orange for other statuses
                        '#EF4444', // Red for blocked/issues
                        '#8B5CF6'  // Purple for review
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Priority Distribution Chart
        const priorityCtx = document.getElementById('priorityChart').getContext('2d');
        new Chart(priorityCtx, {
            type: 'bar',
            data: {
                labels: {!! json_encode(array_keys($metrics['priority_breakdown']->toArray())) !!},
                datasets: [{
                    label: 'Tasks',
                    data: {!! json_encode(array_values($metrics['priority_breakdown']->toArray())) !!},
                    backgroundColor: '#7B68EE',
                    borderColor: '#6A5ACD',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
@endsection
