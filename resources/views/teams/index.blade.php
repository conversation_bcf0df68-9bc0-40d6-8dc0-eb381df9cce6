@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        <i class="fas fa-users mr-2"></i>
        Teams
    </h2>
@endsection

@section('content')
    <div class="space-y-6">
        @forelse($teams as $team)
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full flex items-center justify-center" style="background-color: {{ $team->color ?? '#7B68EE' }}">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    {{ $team->name }}
                                </h3>
                                <p class="text-sm text-gray-500">
                                    {{ $team->spaces_count }} spaces • {{ $teamStats[$team->id]['total_tasks'] }} total tasks
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-gray-900">
                                {{ $teamStats[$team->id]['completion_rate'] }}%
                            </div>
                            <div class="text-sm text-gray-500">
                                Completion Rate
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{{ $teamStats[$team->id]['completed_tasks'] }} / {{ $teamStats[$team->id]['total_tasks'] }} tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-clickup-purple h-2 rounded-full" style="width: {{ $teamStats[$team->id]['completion_rate'] }}%"></div>
                        </div>
                    </div>

                    <!-- Taqnyat Product Folders -->
                    @if(isset($team->taqnyat_folders) && $team->taqnyat_folders->count() > 0)
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Taqnyat Product Folders</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach($team->taqnyat_folders as $folder)
                                    <div class="border border-gray-200 rounded-lg p-3">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                @php
                                                    $iconClass = match($folder->name) {
                                                        'LiveChat [Web]' => 'fas fa-comments text-blue-500',
                                                        'LiveChat [Mobile]' => 'fas fa-mobile-alt text-green-500',
                                                        'Chatbot' => 'fas fa-robot text-purple-500',
                                                        'Email' => 'fas fa-envelope text-red-500',
                                                        'Chatbot AI' => 'fas fa-brain text-indigo-500',
                                                        'Call Bot' => 'fas fa-phone text-orange-500',
                                                        default => 'fas fa-cube text-gray-500'
                                                    };
                                                @endphp
                                                <div class="flex items-center mb-1">
                                                    <i class="{{ $iconClass }} mr-2"></i>
                                                    <h5 class="text-sm font-medium text-gray-900">{{ $folder->name }}</h5>
                                                </div>
                                                <p class="text-xs text-gray-500">
                                                    {{ $folder->lists_count }} lists • {{ $folder->tasks_count }} tasks
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @empty
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6 text-center">
                    <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">
                        No Teams Found
                    </h3>
                    <p class="text-gray-600">
                        Please sync your data first to see teams and their statistics.
                    </p>
                    <button onclick="syncData()" class="mt-4 bg-clickup-purple hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Sync Data
                    </button>
                </div>
            </div>
        @endforelse
    </div>
@endsection
