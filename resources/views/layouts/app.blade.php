<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'ClickUp Dashboard') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS JIT CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'clickup-purple': '#7B68EE',
                        'clickup-blue': '#4285F4',
                        'clickup-green': '#00C875',
                        'clickup-orange': '#FF9500',
                        'clickup-red': '#FF5722',
                        'taqnyat-primary': '#6366F1',
                        'taqnyat-secondary': '#8B5CF6',
                    }
                }
            }
        }
    </script>

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        [x-cloak] { display: none !important; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50" x-data="{ sidebarOpen: false }">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform sidebar-transition lg:translate-x-0"
             :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
            <div class="flex flex-col h-full overflow-hidden">
                <!-- Logo/Header -->
                <div class="flex items-center justify-between h-16 px-6 bg-gradient-to-r from-gray-800 to-gray-900">
                    <div class="flex items-center">
                        <i class="fas fa-chart-line text-white text-2xl mr-3"></i>
                        <h1 class="text-xl font-bold text-white">Dashboard</h1>
                    </div>
                    <button @click="sidebarOpen = false" class="lg:hidden text-white hover:text-gray-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Navigation -->
                <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                    <!-- ClickUp Section -->
                    <div class="mb-6">
                        <!-- ClickUp Header -->
                        <div class="flex items-center px-4 py-3 mb-3 bg-gradient-to-r from-taqnyat-primary to-taqnyat-secondary rounded-lg">
                            <i class="fas fa-rocket text-white text-lg mr-3"></i>
                            <h2 class="text-sm font-bold text-white uppercase tracking-wider">ClickUp</h2>
                        </div>

                        <!-- ClickUp Sub-items -->
                        <div class="ml-4 space-y-1">
                            <a href="{{ route('dashboard') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('dashboard') ? 'bg-taqnyat-primary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-tachometer-alt mr-3"></i>
                                Dashboard
                            </a>

                            <a href="{{ route('teams.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('teams.*') ? 'bg-taqnyat-primary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-users mr-3"></i>
                                Teams
                            </a>

                            <a href="{{ route('tasks.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('tasks.*') ? 'bg-taqnyat-primary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-tasks mr-3"></i>
                                Tasks
                            </a>

                            <a href="{{ route('reports.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('reports.*') || request()->routeIs('taqnyat.reports*') ? 'bg-taqnyat-primary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-chart-bar mr-3"></i>
                                Taqnyat Reports
                            </a>

                            <a href="{{ route('activity.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('activity.*') ? 'bg-taqnyat-primary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-history mr-3"></i>
                                Activity Timeline
                            </a>

                            <!-- Taqnyat Product Dashboard -->
                            <a href="{{ route('taqnyat.dashboard') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 {{ request()->routeIs('taqnyat.dashboard') ? 'bg-taqnyat-secondary text-white' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-rocket mr-3"></i>
                                Product Dashboard
                            </a>

                            <!-- Product Folders Sub-section -->
                            <div class="mt-3 pt-3 border-t border-gray-200">
                                <div class="px-4 py-1 mb-2">
                                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Taqnyat Products</h3>
                                </div>

                                <div class="ml-4 space-y-1">
                                    @php
                                        $products = [
                                            ['name' => 'LiveChat [Web]', 'icon' => 'fas fa-comments', 'color' => 'text-blue-500'],
                                            ['name' => 'LiveChat [Mobile]', 'icon' => 'fas fa-mobile-alt', 'color' => 'text-green-500'],
                                            ['name' => 'Chatbot', 'icon' => 'fas fa-robot', 'color' => 'text-purple-500'],
                                            ['name' => 'Email', 'icon' => 'fas fa-envelope', 'color' => 'text-red-500'],
                                            ['name' => 'Chatbot AI', 'icon' => 'fas fa-brain', 'color' => 'text-indigo-500'],
                                            ['name' => 'Call Bot', 'icon' => 'fas fa-phone', 'color' => 'text-orange-500']
                                        ];
                                    @endphp

                                    @foreach($products as $product)
                                        <a href="{{ route('tasks.index', ['product' => $product['name']]) }}" class="flex items-center px-3 py-1.5 text-xs font-medium rounded-lg transition-colors duration-200 text-gray-600 hover:bg-gray-50">
                                            <i class="{{ $product['icon'] }} {{ $product['color'] }} mr-2"></i>
                                            {{ $product['name'] }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Future Data Sources Section -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="px-4 py-2">
                            <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Other Data Sources</h3>
                            <p class="text-xs text-gray-400 mt-1">Coming soon...</p>
                        </div>
                    </div>
                </nav>

                <!-- Footer -->
                <div class="px-4 py-4 border-t border-gray-200">
                    <button onclick="syncData()" class="w-full bg-taqnyat-primary hover:bg-taqnyat-secondary text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-150 ease-in-out">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Sync Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile sidebar overlay -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden" x-cloak></div>

        <!-- Mobile header -->
        <div class="lg:hidden bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between h-16 px-4">
                <button @click="sidebarOpen = true" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-900">ClickUp Dashboard</h1>
                <button onclick="syncData()" class="text-taqnyat-primary hover:text-taqnyat-secondary">
                    <i class="fas fa-sync-alt text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col lg:ml-64">
            <!-- Page Heading -->
            @hasSection('header')
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="px-4 py-6 sm:px-6 lg:px-8">
                        @yield('header')
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main class="flex-1 py-6">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Flash Messages -->
                    @if (session('success'))
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ session('error') }}</span>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center">
                <i class="fas fa-spinner fa-spin text-clickup-purple text-2xl mr-3"></i>
                <span class="text-lg">Syncing data...</span>
            </div>
        </div>
    </div>

    <script>
        function syncData() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
            
            fetch('/sync-data', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading-overlay').classList.add('hidden');
                document.getElementById('loading-overlay').classList.remove('flex');
                
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error syncing data: ' + data.message);
                }
            })
            .catch(error => {
                document.getElementById('loading-overlay').classList.add('hidden');
                document.getElementById('loading-overlay').classList.remove('flex');
                alert('Error syncing data: ' + error.message);
            });
        }
    </script>
</body>
</html>
