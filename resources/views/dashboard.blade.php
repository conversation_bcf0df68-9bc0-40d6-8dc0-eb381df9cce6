@extends('layouts.app')
@section('header')
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <i class="fas fa-tachometer-alt mr-2"></i>
            Dashboard
        </h2>
        <div class="text-sm text-gray-600">
            Last sync: {{ $lastSync ? $lastSync->format('M d, Y H:i') : 'Never' }}
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Teams Card -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-clickup-purple text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Teams
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ $stats['teams'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spaces Card -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-layer-group text-clickup-blue text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Spaces
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ $stats['spaces'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lists Card -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-list text-clickup-green text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Lists
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ $stats['lists'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tasks Card -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-tasks text-clickup-orange text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    Tasks
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ $stats['tasks'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Task Status Distribution -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Task Status Distribution
                    </h3>
                    <div class="relative h-64">
                        <canvas id="taskStatusChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Tasks by Priority -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Tasks by Priority
                    </h3>
                    <div class="relative h-64">
                        <canvas id="taskPriorityChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-clock mr-2"></i>
                    Recent Tasks
                </h3>
                <div class="flow-root">
                    <ul class="-mb-8">
                        @forelse($recentTasks as $index => $task)
                            <li>
                                <div class="relative pb-8">
                                    @if(!$loop->last)
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                    @endif
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white
                                                @if($task->status['status'] === 'complete') bg-green-500
                                                @elseif($task->status['status'] === 'in progress') bg-blue-500
                                                @else bg-gray-400
                                                @endif">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-900">
                                                    <a href="{{ $task->url }}" target="_blank" class="font-medium hover:text-clickup-purple">
                                                        {{ $task->name }}
                                                    </a>
                                                </p>
                                                <p class="text-sm text-gray-500">
                                                    Status: {{ $task->status['status'] ?? 'Unknown' }}
                                                    @if($task->assignees && count($task->assignees) > 0)
                                                        • Assigned to: {{ $task->assignees[0]['username'] ?? 'Unknown' }}
                                                    @endif
                                                </p>
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                {{ $task->date_updated ? $task->date_updated->diffForHumans() : 'Unknown' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="text-center py-8 text-gray-500">
                                <i class="fas fa-inbox text-4xl mb-4"></i>
                                <p>No recent tasks found. Try syncing your data first.</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Task Status Chart
        const taskStatusCtx = document.getElementById('taskStatusChart').getContext('2d');
        new Chart(taskStatusCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode(array_keys($taskStatusData)) !!},
                datasets: [{
                    data: {!! json_encode(array_values($taskStatusData)) !!},
                    backgroundColor: [
                        '#7B68EE',
                        '#4285F4',
                        '#00C875',
                        '#FF9500',
                        '#FF5722',
                        '#9E9E9E'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Task Priority Chart
        const taskPriorityCtx = document.getElementById('taskPriorityChart').getContext('2d');
        new Chart(taskPriorityCtx, {
            type: 'bar',
            data: {
                labels: {!! json_encode(array_keys($taskPriorityData)) !!},
                datasets: [{
                    label: 'Tasks',
                    data: {!! json_encode(array_values($taskPriorityData)) !!},
                    backgroundColor: '#7B68EE',
                    borderColor: '#6A5ACD',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
@endsection
