@extends('layouts.app')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        <i class="fas fa-tasks mr-2"></i>
        Tasks
    </h2>
@endsection

@section('content')
    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ route('tasks.index') }}" class="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 lg:grid-cols-5 sm:gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}"
                           placeholder="Task name..."
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple sm:text-sm">
                </div>

                <!-- Team Filter -->
                <div>
                    <label for="team" class="block text-sm font-medium text-gray-700">Team</label>
                    <select name="team" id="team" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple sm:text-sm">
                        <option value="">All Teams</option>
                        @foreach($teams as $team)
                            <option value="{{ $team->id }}" {{ request('team') == $team->id ? 'selected' : '' }}>
                                {{ $team->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple sm:text-sm">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                {{ ucfirst($status) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Priority Filter -->
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700">Priority</label>
                    <select name="priority" id="priority" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-clickup-purple focus:border-clickup-purple sm:text-sm">
                        <option value="">All Priorities</option>
                        @foreach($priorities as $priority)
                            <option value="{{ $priority }}" {{ request('priority') == $priority ? 'selected' : '' }}>
                                {{ ucfirst($priority) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-clickup-purple hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Tasks ({{ $tasks->total() }})
                </h3>
                <a href="{{ route('tasks.index') }}" class="text-sm text-clickup-purple hover:text-purple-700">
                    Clear Filters
                </a>
            </div>

            @if($tasks->count() > 0)
                <div class="space-y-4">
                    @foreach($tasks as $task)
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <!-- Status Badge -->
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if(($task->status['status'] ?? '') === 'complete') bg-green-100 text-green-800
                                            @elseif(($task->status['status'] ?? '') === 'in progress') bg-blue-100 text-blue-800
                                            @elseif(($task->status['status'] ?? '') === 'to do') bg-gray-100 text-gray-800
                                            @else bg-yellow-100 text-yellow-800
                                            @endif">
                                            {{ ucfirst($task->status['status'] ?? 'Unknown') }}
                                        </span>

                                        <!-- Priority Badge -->
                                        @if($task->priority && isset($task->priority['priority']))
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($task->priority['priority'] === 'urgent') bg-red-100 text-red-800
                                                @elseif($task->priority['priority'] === 'high') bg-orange-100 text-orange-800
                                                @elseif($task->priority['priority'] === 'normal') bg-blue-100 text-blue-800
                                                @else bg-gray-100 text-gray-800
                                                @endif">
                                                <i class="fas fa-flag mr-1"></i>
                                                {{ ucfirst($task->priority['priority']) }}
                                            </span>
                                        @endif
                                    </div>

                                    <h4 class="text-lg font-medium text-gray-900 mb-2">
                                        <a href="{{ $task->url }}" target="_blank" class="hover:text-clickup-purple">
                                            {{ $task->name }}
                                        </a>
                                    </h4>

                                    @if($task->description)
                                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                                            {{ Str::limit(strip_tags($task->description), 150) }}
                                        </p>
                                    @endif

                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>
                                            <i class="fas fa-layer-group mr-1"></i>
                                            {{ $task->space->name ?? 'Unknown Space' }}
                                        </span>
                                        <span>
                                            <i class="fas fa-list mr-1"></i>
                                            {{ $task->list->name ?? 'Unknown List' }}
                                        </span>
                                        @if($task->assignees && count($task->assignees) > 0)
                                            <span>
                                                <i class="fas fa-user mr-1"></i>
                                                {{ $task->assignees[0]['username'] ?? 'Unknown' }}
                                            </span>
                                        @endif
                                        @if($task->due_date)
                                            <span>
                                                <i class="fas fa-calendar mr-1"></i>
                                                {{ $task->due_date->format('M d, Y') }}
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <div class="ml-4 text-right">
                                    <div class="text-sm text-gray-500">
                                        Updated {{ $task->date_updated ? $task->date_updated->diffForHumans() : 'Unknown' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $tasks->withQueryString()->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-tasks text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">
                        No Tasks Found
                    </h3>
                    <p class="text-gray-600">
                        @if(request()->hasAny(['search', 'team', 'status', 'priority']))
                            Try adjusting your filters or
                            <a href="{{ route('tasks.index') }}" class="text-clickup-purple hover:text-purple-700">clear all filters</a>.
                        @else
                            Please sync your data first to see tasks from your ClickUp workspace.
                        @endif
                    </p>
                </div>
            @endif
        </div>
    </div>
@endsection
