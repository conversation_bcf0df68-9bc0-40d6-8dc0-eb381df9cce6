@extends('layouts.app')

@section('header')
    <div class="flex justify-between items-center">
        <div>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-history mr-2 text-taqnyat-primary"></i>
                Activity Timeline
            </h2>
            <p class="text-sm text-gray-600 mt-1">
                Real-time activity tracking for all Taqnyat product development
            </p>
        </div>
        <div class="flex space-x-3">
            <!-- Time Period Filter -->
            <select id="daysFilter" onchange="updateFilters()" class="border-gray-300 rounded-md shadow-sm focus:ring-taqnyat-primary focus:border-taqnyat-primary text-sm">
                <option value="1" {{ $days == 1 ? 'selected' : '' }}>Last 24 hours</option>
                <option value="7" {{ $days == 7 ? 'selected' : '' }}>Last 7 days</option>
                <option value="30" {{ $days == 30 ? 'selected' : '' }}>Last 30 days</option>
                <option value="60" {{ $days == 60 ? 'selected' : '' }}>Last 60 days</option>
            </select>
        </div>
    </div>
@endsection

@section('content')
    <!-- Activity Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-taqnyat-primary text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Activities</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['total_activities'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tasks text-green-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tasks Updated</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['by_type']['task_updated'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-blue-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tasks Completed</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['by_type']['task_completed'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-plus-circle text-purple-500 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Tasks Created</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $summary['by_type']['task_created'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Product Filter -->
                <div>
                    <label for="productFilter" class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                    <select id="productFilter" onchange="updateFilters()" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-taqnyat-primary focus:border-taqnyat-primary text-sm">
                        <option value="">All Products</option>
                        @foreach($products as $product)
                            <option value="{{ $product }}" {{ $productFilter == $product ? 'selected' : '' }}>
                                {{ $product }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Activity Type Filter -->
                <div>
                    <label for="typeFilter" class="block text-sm font-medium text-gray-700 mb-1">Activity Type</label>
                    <select id="typeFilter" onchange="updateFilters()" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-taqnyat-primary focus:border-taqnyat-primary text-sm">
                        <option value="">All Types</option>
                        @foreach($activityTypes as $type)
                            <option value="{{ $type }}" {{ $typeFilter == $type ? 'selected' : '' }}>
                                {{ ucwords(str_replace('_', ' ', $type)) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Clear Filters -->
                <div class="flex items-end">
                    <button onclick="clearFilters()" class="w-full bg-gray-500 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        <i class="fas fa-times mr-2"></i>
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-stream mr-2"></i>
                Activity Feed
            </h3>
            
            <div id="activityFeed" class="space-y-4">
                @forelse($activities as $activity)
                    <div class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <!-- Activity Icon -->
                        <div class="flex-shrink-0">
                            @php
                                $iconClass = match($activity->activity_type) {
                                    'task_created' => 'fas fa-plus-circle text-green-500',
                                    'task_updated' => 'fas fa-edit text-blue-500',
                                    'task_completed' => 'fas fa-check-circle text-purple-500',
                                    'status_changed' => 'fas fa-exchange-alt text-orange-500',
                                    'data_sync' => 'fas fa-sync-alt text-gray-500',
                                    default => 'fas fa-circle text-gray-400'
                                };
                            @endphp
                            <div class="w-8 h-8 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                                <i class="{{ $iconClass }}"></i>
                            </div>
                        </div>

                        <!-- Activity Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <!-- Product Badge -->
                                    @if($activity->product_folder && $activity->product_folder !== 'System')
                                        @php
                                            $productColor = match($activity->product_folder) {
                                                'LiveChat [Web]' => 'bg-blue-100 text-blue-800',
                                                'LiveChat [Mobile]' => 'bg-green-100 text-green-800',
                                                'Chatbot' => 'bg-purple-100 text-purple-800',
                                                'Email' => 'bg-red-100 text-red-800',
                                                'Chatbot AI' => 'bg-indigo-100 text-indigo-800',
                                                'Call Bot' => 'bg-orange-100 text-orange-800',
                                                default => 'bg-gray-100 text-gray-800'
                                            };
                                        @endphp
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $productColor }}">
                                            {{ $activity->product_folder }}
                                        </span>
                                    @endif

                                    <!-- Activity Type Badge -->
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ ucwords(str_replace('_', ' ', $activity->activity_type)) }}
                                    </span>
                                </div>

                                <!-- Timestamp -->
                                <div class="text-sm text-gray-500">
                                    {{ $activity->activity_date->diffForHumans() }}
                                </div>
                            </div>

                            <!-- Activity Description -->
                            <div class="mt-2">
                                <p class="text-sm text-gray-900">{{ $activity->description }}</p>
                                
                                @if($activity->entity_name && $activity->entity_type === 'task')
                                    <p class="text-xs text-gray-500 mt-1">
                                        Task: <span class="font-medium">{{ $activity->entity_name }}</span>
                                    </p>
                                @endif

                                @if($activity->user_name)
                                    <p class="text-xs text-gray-500 mt-1">
                                        By: <span class="font-medium">{{ $activity->user_name }}</span>
                                    </p>
                                @endif

                                @if($activity->changes && is_array($activity->changes))
                                    <div class="mt-2 text-xs text-gray-600">
                                        <details class="cursor-pointer">
                                            <summary class="font-medium">View Changes</summary>
                                            <pre class="mt-1 bg-gray-50 p-2 rounded text-xs overflow-x-auto">{{ json_encode($activity->changes, JSON_PRETTY_PRINT) }}</pre>
                                        </details>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">No Activities Found</h3>
                        <p class="text-gray-600">
                            @if($productFilter || $typeFilter)
                                No activities match your current filters. Try adjusting the filters or time period.
                            @else
                                No recent activities found for the selected time period.
                            @endif
                        </p>
                    </div>
                @endforelse
            </div>

            <!-- Load More Button -->
            @if($activities->count() >= 50)
                <div class="mt-6 text-center">
                    <button onclick="loadMoreActivities()" id="loadMoreBtn" class="bg-taqnyat-primary hover:bg-taqnyat-secondary text-white px-6 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        <i class="fas fa-chevron-down mr-2"></i>
                        Load More Activities
                    </button>
                </div>
            @endif
        </div>
    </div>

    <script>
        let currentPage = 1;
        let loading = false;

        function updateFilters() {
            const days = document.getElementById('daysFilter').value;
            const product = document.getElementById('productFilter').value;
            const type = document.getElementById('typeFilter').value;
            
            const params = new URLSearchParams();
            if (days) params.append('days', days);
            if (product) params.append('product', product);
            if (type) params.append('type', type);
            
            window.location.href = `{{ route('activity.index') }}?${params.toString()}`;
        }

        function clearFilters() {
            window.location.href = `{{ route('activity.index') }}`;
        }

        function loadMoreActivities() {
            if (loading) return;
            
            loading = true;
            currentPage++;
            
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            loadMoreBtn.disabled = true;
            
            const params = new URLSearchParams(window.location.search);
            params.set('page', currentPage);
            
            fetch(`{{ route('activity.api') }}?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.activities && data.activities.length > 0) {
                        appendActivities(data.activities);
                        
                        if (!data.has_more) {
                            loadMoreBtn.style.display = 'none';
                        } else {
                            loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down mr-2"></i>Load More Activities';
                            loadMoreBtn.disabled = false;
                        }
                    } else {
                        loadMoreBtn.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading activities:', error);
                    loadMoreBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Error Loading';
                })
                .finally(() => {
                    loading = false;
                });
        }

        function appendActivities(activities) {
            const feed = document.getElementById('activityFeed');
            
            activities.forEach(activity => {
                const activityHtml = createActivityHtml(activity);
                feed.insertAdjacentHTML('beforeend', activityHtml);
            });
        }

        function createActivityHtml(activity) {
            // This would create the HTML for each activity item
            // For brevity, returning a simple template
            return `
                <div class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                            <i class="fas fa-circle text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                ${activity.activity_type.replace('_', ' ')}
                            </span>
                            <div class="text-sm text-gray-500">
                                ${new Date(activity.activity_date).toLocaleString()}
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm text-gray-900">${activity.description}</p>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
@endsection
