<?php

namespace App\Console\Commands;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use App\Services\ClickUpApiService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SyncClickUpData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clickup:sync {--full : Perform a full sync of all data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync data from ClickUp API to local database';

    private ClickUpApiService $clickUpService;

    public function __construct(ClickUpApiService $clickUpService)
    {
        parent::__construct();
        $this->clickUpService = $clickUpService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting ClickUp data synchronization...');

        $startTime = microtime(true);

        try {
            // Sync teams first
            $this->syncTeams();

            // Sync spaces for each team
            $this->syncSpaces();

            // Sync folders for each space
            $this->syncFolders();

            // Sync lists for each folder and space
            $this->syncLists();

            // Sync tasks for each list
            $this->syncTasks();

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            $this->info("✅ ClickUp data synchronization completed successfully in {$duration} seconds!");

        } catch (\Exception $e) {
            $this->error("❌ Error during synchronization: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function syncTeams(): void
    {
        $this->info('🔄 Syncing teams...');

        $teams = $this->clickUpService->getTeams();
        $syncedCount = 0;

        foreach ($teams as $teamData) {
            ClickupTeam::updateOrCreate(
                ['clickup_id' => $teamData['id']],
                [
                    'name' => $teamData['name'],
                    'color' => $teamData['color'] ?? null,
                    'avatar' => $teamData['avatar'] ?? null,
                    'members' => $teamData['members'] ?? [],
                    'last_synced_at' => Carbon::now(),
                ]
            );
            $syncedCount++;
        }

        $this->info("✅ Synced {$syncedCount} teams");
    }

    private function syncSpaces(): void
    {
        $this->info('🔄 Syncing spaces...');

        $teams = ClickupTeam::all();
        $syncedCount = 0;

        foreach ($teams as $team) {
            $spaces = $this->clickUpService->getSpaces($team->clickup_id);

            foreach ($spaces as $spaceData) {
                ClickupSpace::updateOrCreate(
                    ['clickup_id' => $spaceData['id']],
                    [
                        'name' => $spaceData['name'],
                        'color' => $spaceData['color'] ?? null,
                        'private' => $spaceData['private'] ?? false,
                        'avatar' => $spaceData['avatar'] ?? null,
                        'statuses' => $spaceData['statuses'] ?? [],
                        'features' => $spaceData['features'] ?? [],
                        'team_id' => $team->id,
                        'last_synced_at' => Carbon::now(),
                    ]
                );
                $syncedCount++;
            }
        }

        $this->info("✅ Synced {$syncedCount} spaces");
    }

    private function syncFolders(): void
    {
        $this->info('🔄 Syncing folders...');

        $spaces = ClickupSpace::all();
        $syncedCount = 0;

        foreach ($spaces as $space) {
            $folders = $this->clickUpService->getFolders($space->clickup_id);

            foreach ($folders as $folderData) {
                ClickupFolder::updateOrCreate(
                    ['clickup_id' => $folderData['id']],
                    [
                        'name' => $folderData['name'],
                        'orderindex' => $folderData['orderindex'] ?? null,
                        'override_statuses' => $folderData['override_statuses'] ?? false,
                        'hidden' => $folderData['hidden'] ?? false,
                        'statuses' => $folderData['statuses'] ?? [],
                        'space_id' => $space->id,
                        'last_synced_at' => Carbon::now(),
                    ]
                );
                $syncedCount++;
            }
        }

        $this->info("✅ Synced {$syncedCount} folders");
    }

    private function syncLists(): void
    {
        $this->info('🔄 Syncing lists...');

        $syncedCount = 0;

        // Sync lists from folders
        $folders = ClickupFolder::all();
        foreach ($folders as $folder) {
            $lists = $this->clickUpService->getLists($folder->clickup_id);

            foreach ($lists as $listData) {
                ClickupList::updateOrCreate(
                    ['clickup_id' => $listData['id']],
                    [
                        'name' => $listData['name'],
                        'orderindex' => $listData['orderindex'] ?? null,
                        'content' => $listData['content'] ?? null,
                        'status' => $listData['status'] ?? null,
                        'priority' => $listData['priority'] ?? null,
                        'assignee' => $listData['assignee'] ?? null,
                        'task_count' => $listData['task_count'] ?? 0,
                        'due_date' => isset($listData['due_date']) ? Carbon::createFromTimestamp($listData['due_date'] / 1000) : null,
                        'start_date' => isset($listData['start_date']) ? Carbon::createFromTimestamp($listData['start_date'] / 1000) : null,
                        'folder_id' => $folder->id,
                        'space_id' => $folder->space_id,
                        'archived' => $listData['archived'] ?? false,
                        'last_synced_at' => Carbon::now(),
                    ]
                );
                $syncedCount++;
            }
        }

        // Sync folderless lists from spaces
        $spaces = ClickupSpace::all();
        foreach ($spaces as $space) {
            $lists = $this->clickUpService->getFolderlessLists($space->clickup_id);

            foreach ($lists as $listData) {
                ClickupList::updateOrCreate(
                    ['clickup_id' => $listData['id']],
                    [
                        'name' => $listData['name'],
                        'orderindex' => $listData['orderindex'] ?? null,
                        'content' => $listData['content'] ?? null,
                        'status' => $listData['status'] ?? null,
                        'priority' => $listData['priority'] ?? null,
                        'assignee' => $listData['assignee'] ?? null,
                        'task_count' => $listData['task_count'] ?? 0,
                        'due_date' => isset($listData['due_date']) ? Carbon::createFromTimestamp($listData['due_date'] / 1000) : null,
                        'start_date' => isset($listData['start_date']) ? Carbon::createFromTimestamp($listData['start_date'] / 1000) : null,
                        'folder_id' => null,
                        'space_id' => $space->id,
                        'archived' => $listData['archived'] ?? false,
                        'last_synced_at' => Carbon::now(),
                    ]
                );
                $syncedCount++;
            }
        }

        $this->info("✅ Synced {$syncedCount} lists");
    }

    private function syncTasks(): void
    {
        $this->info('🔄 Syncing tasks...');

        $lists = ClickupList::all();
        $syncedCount = 0;

        foreach ($lists as $list) {
            $this->info("  📋 Syncing tasks for list: {$list->name}");

            $tasks = $this->clickUpService->getTasks($list->clickup_id);

            foreach ($tasks as $taskData) {
                ClickupTask::updateOrCreate(
                    ['clickup_id' => $taskData['id']],
                    [
                        'name' => $taskData['name'],
                        'description' => $taskData['description'] ?? null,
                        'status' => $taskData['status'] ?? [],
                        'orderindex' => $taskData['orderindex'] ?? null,
                        'date_created' => isset($taskData['date_created']) ? Carbon::createFromTimestamp($taskData['date_created'] / 1000) : null,
                        'date_updated' => isset($taskData['date_updated']) ? Carbon::createFromTimestamp($taskData['date_updated'] / 1000) : null,
                        'date_closed' => isset($taskData['date_closed']) ? Carbon::createFromTimestamp($taskData['date_closed'] / 1000) : null,
                        'date_done' => isset($taskData['date_done']) ? Carbon::createFromTimestamp($taskData['date_done'] / 1000) : null,
                        'assignees' => $taskData['assignees'] ?? [],
                        'watchers' => $taskData['watchers'] ?? [],
                        'checklists' => $taskData['checklists'] ?? [],
                        'tags' => $taskData['tags'] ?? [],
                        'parent' => $taskData['parent'] ?? null,
                        'priority' => $taskData['priority'] ?? null,
                        'due_date' => isset($taskData['due_date']) ? Carbon::createFromTimestamp($taskData['due_date'] / 1000) : null,
                        'start_date' => isset($taskData['start_date']) ? Carbon::createFromTimestamp($taskData['start_date'] / 1000) : null,
                        'points' => $taskData['points'] ?? null,
                        'time_estimate' => $taskData['time_estimate'] ?? null,
                        'time_spent' => $taskData['time_spent'] ?? null,
                        'custom_fields' => $taskData['custom_fields'] ?? [],
                        'dependencies' => $taskData['dependencies'] ?? [],
                        'linked_tasks' => $taskData['linked_tasks'] ?? [],
                        'team_id' => $taskData['team_id'] ?? null,
                        'url' => $taskData['url'] ?? null,
                        'permission_level' => $taskData['permission_level'] ?? null,
                        'list_id' => $list->id,
                        'folder_id' => $list->folder_id,
                        'space_id' => $list->space_id,
                        'last_synced_at' => Carbon::now(),
                    ]
                );
                $syncedCount++;
            }
        }

        $this->info("✅ Synced {$syncedCount} tasks");
    }
}
