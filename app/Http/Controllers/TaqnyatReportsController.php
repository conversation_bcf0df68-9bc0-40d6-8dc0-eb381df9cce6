<?php

namespace App\Http\Controllers;

use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TaqnyatReportsController extends Controller
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    public function index(Request $request)
    {
        // Get Taqnyat space
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return redirect()->route('dashboard')->with('error', 'Taqnyat space not found');
        }

        // Get time period filter (default to last 30 days)
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays((int)$period);

        // Get the 6 target product folders with tasks
        $productFolders = ClickupFolder::where('space_id', $taqnyatSpace->id)
            ->whereIn('name', $this->targetProducts)
            ->with(['lists.tasks' => function($query) use ($startDate) {
                $query->where('date_updated', '>=', $startDate);
            }])
            ->get();

        // Calculate comprehensive analytics
        $analytics = $this->calculateAnalytics($productFolders, $startDate);

        // Get task completion trends
        $completionTrends = $this->getCompletionTrends($productFolders, $period);

        // Get bottleneck analysis
        $bottlenecks = $this->getBottleneckAnalysis($productFolders);

        // Get velocity metrics
        $velocityMetrics = $this->getVelocityMetrics($productFolders, $startDate);

        return view('taqnyat.reports', compact(
            'taqnyatSpace',
            'productFolders',
            'analytics',
            'completionTrends',
            'bottlenecks',
            'velocityMetrics',
            'period'
        ));
    }

    public function taskDetails(Request $request)
    {
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return response()->json(['error' => 'Taqnyat space not found'], 404);
        }

        $query = ClickupTask::whereHas('space', function($q) use ($taqnyatSpace) {
            $q->where('id', $taqnyatSpace->id);
        })->whereHas('folder', function($q) {
            $q->whereIn('name', $this->targetProducts);
        })->with(['list', 'folder', 'space']);

        // Apply filters
        if ($request->filled('product')) {
            $query->whereHas('folder', function($q) use ($request) {
                $q->where('name', $request->product);
            });
        }

        if ($request->filled('status')) {
            $query->whereRaw("json_extract(status, '$.status') = ?", [$request->status]);
        }

        if ($request->filled('priority')) {
            $query->whereRaw("json_extract(priority, '$.priority') = ?", [$request->priority]);
        }

        if ($request->filled('assignee')) {
            $query->whereRaw("json_extract(assignees, '$[0].username') = ?", [$request->assignee]);
        }

        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('date_updated', '>=', Carbon::parse($request->date_from));
        }

        if ($request->filled('date_to')) {
            $query->where('date_updated', '<=', Carbon::parse($request->date_to));
        }

        $tasks = $query->orderBy('date_updated', 'desc')->paginate(50);

        return response()->json([
            'tasks' => $tasks->items(),
            'pagination' => [
                'current_page' => $tasks->currentPage(),
                'last_page' => $tasks->lastPage(),
                'per_page' => $tasks->perPage(),
                'total' => $tasks->total()
            ]
        ]);
    }

    public function exportTasks(Request $request)
    {
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return response()->json(['error' => 'Taqnyat space not found'], 404);
        }

        $query = ClickupTask::whereHas('space', function($q) use ($taqnyatSpace) {
            $q->where('id', $taqnyatSpace->id);
        })->whereHas('folder', function($q) {
            $q->whereIn('name', $this->targetProducts);
        })->with(['list', 'folder', 'space']);

        // Apply same filters as taskDetails
        if ($request->filled('product')) {
            $query->whereHas('folder', function($q) use ($request) {
                $q->where('name', $request->product);
            });
        }

        $tasks = $query->get();

        // Generate CSV content
        $csvContent = $this->generateTasksCsv($tasks);

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="taqnyat-tasks-' . date('Y-m-d') . '.csv"');
    }

    private function calculateAnalytics($productFolders, $startDate)
    {
        $analytics = [];

        foreach ($productFolders as $folder) {
            $allTasks = $folder->lists->flatMap->tasks;
            $recentTasks = $allTasks->filter(function($task) use ($startDate) {
                return $task->date_updated && $task->date_updated->gte($startDate);
            });

            $totalTasks = $allTasks->count();
            $completedTasks = $allTasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();

            $recentCompletedTasks = $recentTasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();

            $inProgressTasks = $allTasks->filter(function($task) {
                $status = $task->status['status'] ?? '';
                return in_array($status, ['in progress', 'in review', 'testing']);
            })->count();

            $blockedTasks = $allTasks->filter(function($task) {
                $status = $task->status['status'] ?? '';
                return in_array($status, ['blocked', 'on hold', 'waiting']);
            })->count();

            // Calculate average completion time for recent completed tasks
            $avgCompletionTime = $this->calculateAverageCompletionTime($recentTasks->where('status.status', 'complete'));

            $analytics[$folder->name] = [
                'total_tasks' => $totalTasks,
                'completed_tasks' => $completedTasks,
                'recent_completed' => $recentCompletedTasks,
                'in_progress_tasks' => $inProgressTasks,
                'blocked_tasks' => $blockedTasks,
                'completion_rate' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0,
                'recent_activity' => $recentTasks->count(),
                'avg_completion_time' => $avgCompletionTime,
                'velocity' => $recentCompletedTasks, // Tasks completed in period
            ];
        }

        return $analytics;
    }

    private function getCompletionTrends($productFolders, $period)
    {
        $trends = [];
        $days = (int)$period;

        foreach ($productFolders as $folder) {
            $folderTrends = [];

            for ($i = $days - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dayStart = $date->copy()->startOfDay();
                $dayEnd = $date->copy()->endOfDay();

                $completedOnDay = $folder->lists->flatMap->tasks->filter(function($task) use ($dayStart, $dayEnd) {
                    return $task->date_done &&
                           $task->date_done->between($dayStart, $dayEnd);
                })->count();

                $folderTrends[] = [
                    'date' => $date->format('Y-m-d'),
                    'completed' => $completedOnDay
                ];
            }

            $trends[$folder->name] = $folderTrends;
        }

        return $trends;
    }

    private function getBottleneckAnalysis($productFolders)
    {
        $bottlenecks = [];

        foreach ($productFolders as $folder) {
            $tasks = $folder->lists->flatMap->tasks;
            $statusCounts = [];
            $longRunningTasks = [];

            foreach ($tasks as $task) {
                $status = $task->status['status'] ?? 'Unknown';
                $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;

                // Identify long-running tasks (in progress for more than 7 days)
                if (in_array($status, ['in progress', 'in review']) && $task->date_updated) {
                    $daysSinceUpdate = $task->date_updated->diffInDays(Carbon::now());
                    if ($daysSinceUpdate > 7) {
                        $longRunningTasks[] = [
                            'name' => $task->name,
                            'status' => $status,
                            'days_since_update' => $daysSinceUpdate,
                            'assignee' => $task->assignees[0]['username'] ?? 'Unassigned'
                        ];
                    }
                }
            }

            $bottlenecks[$folder->name] = [
                'status_distribution' => $statusCounts,
                'long_running_tasks' => collect($longRunningTasks)->sortByDesc('days_since_update')->take(5)->values()->all()
            ];
        }

        return $bottlenecks;
    }

    private function getVelocityMetrics($productFolders, $startDate)
    {
        $velocity = [];

        foreach ($productFolders as $folder) {
            $tasks = $folder->lists->flatMap->tasks;
            $recentTasks = $tasks->filter(function($task) use ($startDate) {
                return $task->date_updated && $task->date_updated->gte($startDate);
            });

            $completedInPeriod = $recentTasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();

            $createdInPeriod = $recentTasks->filter(function($task) use ($startDate) {
                return $task->date_created && $task->date_created->gte($startDate);
            })->count();

            $velocity[$folder->name] = [
                'completed' => $completedInPeriod,
                'created' => $createdInPeriod,
                'net_progress' => $completedInPeriod - $createdInPeriod,
                'throughput' => $completedInPeriod / max(1, $startDate->diffInDays(Carbon::now()))
            ];
        }

        return $velocity;
    }

    private function calculateAverageCompletionTime($completedTasks)
    {
        if ($completedTasks->isEmpty()) {
            return 0;
        }

        $totalDays = 0;
        $count = 0;

        foreach ($completedTasks as $task) {
            if ($task->date_created && $task->date_done) {
                $totalDays += $task->date_created->diffInDays($task->date_done);
                $count++;
            }
        }

        return $count > 0 ? round($totalDays / $count, 1) : 0;
    }

    private function generateTasksCsv($tasks)
    {
        $headers = [
            'Task Name',
            'Product',
            'List',
            'Status',
            'Priority',
            'Assignee',
            'Created Date',
            'Updated Date',
            'Due Date',
            'Description',
            'URL'
        ];

        $csvContent = implode(',', $headers) . "\n";

        foreach ($tasks as $task) {
            $row = [
                '"' . str_replace('"', '""', $task->name) . '"',
                '"' . str_replace('"', '""', $task->folder->name ?? '') . '"',
                '"' . str_replace('"', '""', $task->list->name ?? '') . '"',
                '"' . str_replace('"', '""', $task->status['status'] ?? '') . '"',
                '"' . str_replace('"', '""', $task->priority['priority'] ?? 'No Priority') . '"',
                '"' . str_replace('"', '""', $task->assignees[0]['username'] ?? 'Unassigned') . '"',
                '"' . ($task->date_created ? $task->date_created->format('Y-m-d H:i:s') : '') . '"',
                '"' . ($task->date_updated ? $task->date_updated->format('Y-m-d H:i:s') : '') . '"',
                '"' . ($task->due_date ? $task->due_date->format('Y-m-d H:i:s') : '') . '"',
                '"' . str_replace('"', '""', strip_tags($task->description ?? '')) . '"',
                '"' . str_replace('"', '""', $task->url ?? '') . '"'
            ];

            $csvContent .= implode(',', $row) . "\n";
        }

        return $csvContent;
    }
}
