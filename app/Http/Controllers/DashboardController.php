<?php

namespace App\Http\Controllers;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Get basic stats
        $stats = [
            'teams' => ClickupTeam::count(),
            'spaces' => ClickupSpace::count(),
            'lists' => ClickupList::count(),
            'tasks' => ClickupTask::count(),
        ];

        // Get last sync time
        $lastSync = ClickupTask::latest('last_synced_at')->value('last_synced_at');

        // Get task status distribution (SQLite compatible)
        $taskStatusData = [];
        $tasks = ClickupTask::all();
        foreach ($tasks as $task) {
            $status = $task->status['status'] ?? 'Unknown';
            $taskStatusData[$status] = ($taskStatusData[$status] ?? 0) + 1;
        }

        // Get task priority distribution (SQLite compatible)
        $taskPriorityData = [];
        foreach ($tasks as $task) {
            $priority = $task->priority['priority'] ?? 'No Priority';
            $taskPriorityData[$priority] = ($taskPriorityData[$priority] ?? 0) + 1;
        }

        // Get recent tasks
        $recentTasks = ClickupTask::with(['list', 'space'])
            ->orderBy('date_updated', 'desc')
            ->limit(10)
            ->get();

        return view('dashboard', compact(
            'stats',
            'lastSync',
            'taskStatusData',
            'taskPriorityData',
            'recentTasks'
        ));
    }
}
