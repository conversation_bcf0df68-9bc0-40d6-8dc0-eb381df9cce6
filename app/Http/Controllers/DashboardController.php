<?php

namespace App\Http\Controllers;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Get basic stats
        $stats = [
            'teams' => ClickupTeam::count(),
            'spaces' => ClickupSpace::count(),
            'lists' => ClickupList::count(),
            'tasks' => ClickupTask::count(),
        ];

        // Get last sync time
        $lastSync = ClickupTask::latest('last_synced_at')->value('last_synced_at');

        // Get task status distribution
        $taskStatusData = ClickupTask::select(
            DB::raw("JSON_UNQUOTE(JSON_EXTRACT(status, '$.status')) as status_name"),
            DB::raw('COUNT(*) as count')
        )
        ->groupBy('status_name')
        ->pluck('count', 'status_name')
        ->toArray();

        // Get task priority distribution
        $taskPriorityData = ClickupTask::select(
            DB::raw("COALESCE(JSON_UNQUOTE(JSON_EXTRACT(priority, '$.priority')), 'No Priority') as priority_name"),
            DB::raw('COUNT(*) as count')
        )
        ->groupBy('priority_name')
        ->pluck('count', 'priority_name')
        ->toArray();

        // Get recent tasks
        $recentTasks = ClickupTask::with(['list', 'space'])
            ->orderBy('date_updated', 'desc')
            ->limit(10)
            ->get();

        return view('dashboard', compact(
            'stats',
            'lastSync',
            'taskStatusData',
            'taskPriorityData',
            'recentTasks'
        ));
    }
}
