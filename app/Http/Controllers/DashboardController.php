<?php

namespace App\Http\Controllers;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    public function index()
    {
        // Get Taqnyat space only
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return view('dashboard')->with('error', 'Taqnyat space not found');
        }

        // Get basic stats for Taqnyat only
        $taqnyatFolders = ClickupFolder::where('space_id', $taqnyatSpace->id)
            ->whereIn('name', $this->targetProducts)
            ->with(['lists.tasks'])
            ->get();

        $taqnyatLists = ClickupList::where('space_id', $taqnyatSpace->id)
            ->whereHas('folder', function($query) {
                $query->whereIn('name', $this->targetProducts);
            })
            ->get();

        $taqnyatTasks = ClickupTask::where('space_id', $taqnyatSpace->id)
            ->whereHas('folder', function($query) {
                $query->whereIn('name', $this->targetProducts);
            })
            ->get();

        $stats = [
            'teams' => 1, // Only Taqnyat
            'spaces' => 1, // Only Taqnyat space
            'folders' => $taqnyatFolders->count(),
            'lists' => $taqnyatLists->count(),
            'tasks' => $taqnyatTasks->count(),
        ];

        // Get last sync time for Taqnyat tasks only
        $lastSync = $taqnyatTasks->max('last_synced_at');

        // Get task status distribution for Taqnyat only (SQLite compatible)
        $taskStatusData = [];
        foreach ($taqnyatTasks as $task) {
            $status = $task->status['status'] ?? 'Unknown';
            $taskStatusData[$status] = ($taskStatusData[$status] ?? 0) + 1;
        }

        // Get task priority distribution for Taqnyat only (SQLite compatible)
        $taskPriorityData = [];
        foreach ($taqnyatTasks as $task) {
            $priority = $task->priority['priority'] ?? 'No Priority';
            $taskPriorityData[$priority] = ($taskPriorityData[$priority] ?? 0) + 1;
        }

        // Get recent tasks from Taqnyat only
        $recentTasks = $taqnyatTasks->sortByDesc('date_updated')->take(10);

        // Get Taqnyat summary
        $taqnyatSummary = $this->getTaqnyatSummary();

        return view('dashboard', compact(
            'stats',
            'lastSync',
            'taskStatusData',
            'taskPriorityData',
            'recentTasks',
            'taqnyatSummary'
        ));
    }

    private function getTaqnyatSummary()
    {
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return null;
        }

        $targetProducts = [
            'LiveChat [Web]',
            'LiveChat [Mobile]',
            'Chatbot',
            'Email',
            'Chatbot AI',
            'Call Bot'
        ];

        $productFolders = ClickupFolder::where('space_id', $taqnyatSpace->id)
            ->whereIn('name', $targetProducts)
            ->with(['lists.tasks'])
            ->get();

        $totalTasks = 0;
        $completedTasks = 0;
        $productCount = $productFolders->count();

        foreach ($productFolders as $folder) {
            $tasks = $folder->lists->flatMap->tasks;
            $totalTasks += $tasks->count();
            $completedTasks += $tasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();
        }

        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0;

        return [
            'product_count' => $productCount,
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'completion_rate' => $completionRate
        ];
    }
}
