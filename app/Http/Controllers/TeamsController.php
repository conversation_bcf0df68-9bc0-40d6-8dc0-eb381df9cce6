<?php

namespace App\Http\Controllers;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupTask;
use Illuminate\Http\Request;

class TeamsController extends Controller
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    public function index()
    {
        // Get only Taqnyat space and team
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->with('team')->first();

        if (!$taqnyatSpace || !$taqnyatSpace->team) {
            return view('teams.index')->with('error', 'Taqnyat team/space not found');
        }

        // Create a collection with only the Taqnyat team
        $teams = collect([$taqnyatSpace->team]);

        // Get task completion stats for Taqnyat team only (SQLite compatible)
        $teamStats = [];
        $team = $taqnyatSpace->team;

        $teamTasks = ClickupTask::where('space_id', $taqnyatSpace->id)
            ->whereHas('folder', function($query) {
                $query->whereIn('name', $this->targetProducts);
            })
            ->get();

        $totalTasks = $teamTasks->count();
        $completedTasks = $teamTasks->filter(function($task) {
            return ($task->status['status'] ?? '') === 'complete';
        })->count();

        $teamStats[$team->id] = [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'completion_rate' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0
        ];

        // Add Taqnyat-specific spaces info
        $taqnyatFolders = ClickupFolder::where('space_id', $taqnyatSpace->id)
            ->whereIn('name', $this->targetProducts)
            ->withCount(['lists', 'tasks'])
            ->get();

        $team->taqnyat_spaces = collect([$taqnyatSpace]);
        $team->taqnyat_folders = $taqnyatFolders;

        return view('teams.index', compact('teams', 'teamStats'));
    }
}
