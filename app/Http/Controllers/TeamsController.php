<?php

namespace App\Http\Controllers;

use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TeamsController extends Controller
{
    public function index()
    {
        $teams = ClickupTeam::withCount(['spaces'])
            ->with(['spaces' => function($query) {
                $query->withCount(['tasks', 'lists']);
            }])
            ->get();

        // Get task completion stats for each team (SQLite compatible)
        $teamStats = [];
        foreach ($teams as $team) {
            $teamTasks = ClickupTask::whereHas('space', function($query) use ($team) {
                $query->where('team_id', $team->id);
            })->get();

            $totalTasks = $teamTasks->count();
            $completedTasks = $teamTasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();

            $teamStats[$team->id] = [
                'total_tasks' => $totalTasks,
                'completed_tasks' => $completedTasks,
                'completion_rate' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0
            ];
        }

        return view('teams.index', compact('teams', 'teamStats'));
    }
}
