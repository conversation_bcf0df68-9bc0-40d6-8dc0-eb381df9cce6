<?php

namespace App\Http\Controllers;

use App\Models\ClickupTask;
use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupList;
use Illuminate\Http\Request;

class TasksController extends Controller
{
    public function index(Request $request)
    {
        $query = ClickupTask::with(['list', 'space', 'space.team']);

        // Filter by status (SQLite compatible)
        if ($request->filled('status')) {
            $query->whereRaw("json_extract(status, '$.status') = ?", [$request->status]);
        }

        // Filter by team
        if ($request->filled('team')) {
            $query->whereHas('space.team', function($q) use ($request) {
                $q->where('id', $request->team);
            });
        }

        // Filter by priority (SQLite compatible)
        if ($request->filled('priority')) {
            $query->whereRaw("json_extract(priority, '$.priority') = ?", [$request->priority]);
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $tasks = $query->orderBy('date_updated', 'desc')->paginate(20);

        // Get filter options (SQLite compatible)
        $teams = ClickupTeam::all();

        // Get unique statuses
        $allTasks = ClickupTask::all();
        $statuses = $allTasks->pluck('status')
            ->map(function($status) {
                return $status['status'] ?? 'Unknown';
            })
            ->unique()
            ->values();

        // Get unique priorities
        $priorities = $allTasks->whereNotNull('priority')
            ->pluck('priority')
            ->map(function($priority) {
                return $priority['priority'] ?? 'No Priority';
            })
            ->unique()
            ->values();

        return view('tasks.index', compact('tasks', 'teams', 'statuses', 'priorities'));
    }
}
