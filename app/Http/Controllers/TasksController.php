<?php

namespace App\Http\Controllers;

use App\Models\ClickupTask;
use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupList;
use Illuminate\Http\Request;

class TasksController extends Controller
{
    public function index(Request $request)
    {
        $query = ClickupTask::with(['list', 'space', 'space.team']);

        // Filter by status
        if ($request->filled('status')) {
            $query->whereJsonContains('status->status', $request->status);
        }

        // Filter by team
        if ($request->filled('team')) {
            $query->whereHas('space.team', function($q) use ($request) {
                $q->where('id', $request->team);
            });
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->whereJsonContains('priority->priority', $request->priority);
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $tasks = $query->orderBy('date_updated', 'desc')->paginate(20);

        // Get filter options
        $teams = ClickupTeam::all();
        $statuses = ClickupTask::select('status')->distinct()->get()
            ->pluck('status')
            ->map(function($status) {
                return $status['status'] ?? 'Unknown';
            })
            ->unique()
            ->values();

        $priorities = ClickupTask::whereNotNull('priority')->select('priority')->distinct()->get()
            ->pluck('priority')
            ->map(function($priority) {
                return $priority['priority'] ?? 'No Priority';
            })
            ->unique()
            ->values();

        return view('tasks.index', compact('tasks', 'teams', 'statuses', 'priorities'));
    }
}
