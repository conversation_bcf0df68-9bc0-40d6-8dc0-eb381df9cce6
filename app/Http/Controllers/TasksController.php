<?php

namespace App\Http\Controllers;

use App\Models\ClickupTask;
use App\Models\ClickupTeam;
use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupList;
use Illuminate\Http\Request;

class TasksController extends Controller
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    public function index(Request $request)
    {
        // Get Taqnyat space only
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return view('tasks.index')->with('error', 'Taqnyat space not found');
        }

        // Base query for Taqnyat tasks only
        $query = ClickupTask::with(['list', 'folder', 'space'])
            ->where('space_id', $taqnyatSpace->id)
            ->whereHas('folder', function($q) {
                $q->whereIn('name', $this->targetProducts);
            });

        // Filter by product folder
        if ($request->filled('product')) {
            $query->whereHas('folder', function($q) use ($request) {
                $q->where('name', $request->product);
            });
        }

        // Filter by status (SQLite compatible)
        if ($request->filled('status')) {
            $query->whereRaw("json_extract(status, '$.status') = ?", [$request->status]);
        }

        // Filter by priority (SQLite compatible)
        if ($request->filled('priority')) {
            $query->whereRaw("json_extract(priority, '$.priority') = ?", [$request->priority]);
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $tasks = $query->orderBy('date_updated', 'desc')->paginate(20);

        // Get filter options for Taqnyat only (SQLite compatible)
        $taqnyatTasks = ClickupTask::where('space_id', $taqnyatSpace->id)
            ->whereHas('folder', function($q) {
                $q->whereIn('name', $this->targetProducts);
            })
            ->get();

        // Get unique statuses from Taqnyat tasks
        $statuses = $taqnyatTasks->pluck('status')
            ->map(function($status) {
                return $status['status'] ?? 'Unknown';
            })
            ->unique()
            ->values();

        // Get unique priorities from Taqnyat tasks
        $priorities = $taqnyatTasks->whereNotNull('priority')
            ->pluck('priority')
            ->map(function($priority) {
                return $priority['priority'] ?? 'No Priority';
            })
            ->unique()
            ->values();

        // Get product folders for filter
        $products = $this->targetProducts;

        return view('tasks.index', compact('tasks', 'statuses', 'priorities', 'products'));
    }
}
