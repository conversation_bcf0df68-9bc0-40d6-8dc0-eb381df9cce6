<?php

namespace App\Http\Controllers;

use App\Models\ClickupSpace;
use App\Models\ClickupFolder;
use App\Models\ClickupList;
use App\Models\ClickupTask;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TaqnyatDashboardController extends Controller
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    public function index()
    {
        // Get Taqnyat space
        $taqnyatSpace = ClickupSpace::where('name', 'Taqnyat')->first();

        if (!$taqnyatSpace) {
            return redirect()->route('dashboard')->with('error', 'Taqnyat space not found');
        }

        // Get the 6 target product folders
        $productFolders = ClickupFolder::where('space_id', $taqnyatSpace->id)
            ->whereIn('name', $this->targetProducts)
            ->with(['lists.tasks'])
            ->get();

        // Calculate metrics for each product
        $productMetrics = [];
        $overallStats = [
            'total_tasks' => 0,
            'completed_tasks' => 0,
            'in_progress_tasks' => 0,
            'todo_tasks' => 0,
            'total_lists' => 0
        ];

        foreach ($productFolders as $folder) {
            $tasks = $folder->lists->flatMap->tasks;

            $totalTasks = $tasks->count();
            $completedTasks = $tasks->filter(function($task) {
                return ($task->status['status'] ?? '') === 'complete';
            })->count();

            $inProgressTasks = $tasks->filter(function($task) {
                $status = $task->status['status'] ?? '';
                return in_array($status, ['in progress', 'in review', 'testing']);
            })->count();

            $todoTasks = $tasks->filter(function($task) {
                $status = $task->status['status'] ?? '';
                return in_array($status, ['to do', 'open', 'new']);
            })->count();

            // Get recent tasks (updated in last 7 days)
            $recentTasks = $tasks->filter(function($task) {
                return $task->date_updated && $task->date_updated->gte(Carbon::now()->subDays(7));
            })->sortByDesc('date_updated')->take(5);

            // Calculate status distribution
            $statusDistribution = [];
            foreach ($tasks as $task) {
                $status = $task->status['status'] ?? 'Unknown';
                $statusDistribution[$status] = ($statusDistribution[$status] ?? 0) + 1;
            }

            // Calculate priority distribution
            $priorityDistribution = [];
            foreach ($tasks as $task) {
                $priority = $task->priority['priority'] ?? 'No Priority';
                $priorityDistribution[$priority] = ($priorityDistribution[$priority] ?? 0) + 1;
            }

            $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 1) : 0;

            $productMetrics[$folder->name] = [
                'folder' => $folder,
                'total_tasks' => $totalTasks,
                'completed_tasks' => $completedTasks,
                'in_progress_tasks' => $inProgressTasks,
                'todo_tasks' => $todoTasks,
                'completion_rate' => $completionRate,
                'total_lists' => $folder->lists->count(),
                'recent_tasks' => $recentTasks,
                'status_distribution' => $statusDistribution,
                'priority_distribution' => $priorityDistribution,
                'health_score' => $this->calculateHealthScore($completionRate, $recentTasks->count(), $totalTasks)
            ];

            // Add to overall stats
            $overallStats['total_tasks'] += $totalTasks;
            $overallStats['completed_tasks'] += $completedTasks;
            $overallStats['in_progress_tasks'] += $inProgressTasks;
            $overallStats['todo_tasks'] += $todoTasks;
            $overallStats['total_lists'] += $folder->lists->count();
        }

        // Calculate overall completion rate
        $overallStats['completion_rate'] = $overallStats['total_tasks'] > 0
            ? round(($overallStats['completed_tasks'] / $overallStats['total_tasks']) * 100, 1)
            : 0;

        // Sort products by name to maintain consistent order
        $sortedProducts = collect($this->targetProducts)->mapWithKeys(function($productName) use ($productMetrics) {
            return [$productName => $productMetrics[$productName] ?? null];
        })->filter();

        return view('taqnyat.dashboard', compact('taqnyatSpace', 'sortedProducts', 'overallStats'));
    }

    public function productDetail($folderId)
    {
        $folder = ClickupFolder::with(['lists.tasks', 'space'])->findOrFail($folderId);

        // Ensure this is a Taqnyat product folder
        if ($folder->space->name !== 'Taqnyat' || !in_array($folder->name, $this->targetProducts)) {
            return redirect()->route('taqnyat.dashboard')->with('error', 'Invalid product folder');
        }

        $tasks = $folder->lists->flatMap->tasks;

        // Detailed metrics for this product
        $metrics = $this->calculateDetailedMetrics($tasks);

        return view('taqnyat.product-detail', compact('folder', 'tasks', 'metrics'));
    }

    private function calculateHealthScore($completionRate, $recentActivity, $totalTasks)
    {
        // Health score based on completion rate (60%), recent activity (25%), and task volume (15%)
        $completionScore = $completionRate * 0.6;
        $activityScore = min($recentActivity * 5, 25); // Max 25 points for activity
        $volumeScore = min($totalTasks * 0.5, 15); // Max 15 points for volume

        return round($completionScore + $activityScore + $volumeScore, 1);
    }

    private function calculateDetailedMetrics($tasks)
    {
        // Implementation for detailed metrics
        return [
            'total_tasks' => $tasks->count(),
            'status_breakdown' => $tasks->groupBy(function($task) {
                return $task->status['status'] ?? 'Unknown';
            })->map->count(),
            'priority_breakdown' => $tasks->groupBy(function($task) {
                return $task->priority['priority'] ?? 'No Priority';
            })->map->count(),
            'assignee_breakdown' => $tasks->groupBy(function($task) {
                return $task->assignees[0]['username'] ?? 'Unassigned';
            })->map->count(),
        ];
    }
}
