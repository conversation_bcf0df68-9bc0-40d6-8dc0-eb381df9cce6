<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class SyncController extends Controller
{
    public function sync(Request $request)
    {
        try {
            // Log the sync attempt
            \Log::info('Sync attempt started');

            // Run the sync command
            Artisan::call('clickup:sync');

            // Log success
            \Log::info('Sync completed successfully');

            return response()->json([
                'success' => true,
                'message' => 'Data synchronized successfully!'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Sync failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error syncing data: ' . $e->getMessage()
            ], 500);
        }
    }
}
