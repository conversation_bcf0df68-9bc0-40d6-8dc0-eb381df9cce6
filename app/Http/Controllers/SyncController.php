<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class SyncController extends Controller
{
    public function sync(Request $request)
    {
        try {
            // Run the sync command
            Artisan::call('clickup:sync');

            return response()->json([
                'success' => true,
                'message' => 'Data synchronized successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error syncing data: ' . $e->getMessage()
            ], 500);
        }
    }
}
