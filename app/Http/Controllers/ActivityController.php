<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Services\ActivityTrackingService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ActivityController extends Controller
{
    private ActivityTrackingService $activityService;

    public function __construct(ActivityTrackingService $activityService)
    {
        $this->activityService = $activityService;
    }

    /**
     * Display activity timeline
     */
    public function index(Request $request)
    {
        $productFilter = $request->get('product');
        $typeFilter = $request->get('type');
        $days = (int) $request->get('days', 7);

        // Get activity timeline
        $activities = $this->getFilteredActivities($productFilter, $typeFilter, $days);

        // Get activity summary
        $summary = $this->activityService->getActivitySummary($days);

        // Get available filters
        $products = [
            'LiveChat [Web]',
            'LiveChat [Mobile]',
            'Chatbot',
            'Email',
            'Chatbot AI',
            'Call Bot'
        ];

        $activityTypes = ActivityLog::distinct()
            ->pluck('activity_type')
            ->filter()
            ->values();

        return view('activity.index', compact(
            'activities',
            'summary',
            'products',
            'activityTypes',
            'productFilter',
            'typeFilter',
            'days'
        ));
    }

    /**
     * Get activity data for API
     */
    public function getActivities(Request $request)
    {
        $productFilter = $request->get('product');
        $typeFilter = $request->get('type');
        $days = (int) $request->get('days', 7);
        $page = (int) $request->get('page', 1);
        $perPage = 20;

        $activities = $this->getFilteredActivities($productFilter, $typeFilter, $days, $page, $perPage);

        return response()->json([
            'activities' => $activities,
            'has_more' => $activities->count() === $perPage
        ]);
    }

    /**
     * Get filtered activities
     */
    private function getFilteredActivities($productFilter = null, $typeFilter = null, $days = 7, $page = 1, $perPage = 50)
    {
        $startDate = Carbon::now()->subDays($days);

        $query = ActivityLog::where('activity_date', '>=', $startDate);

        if ($productFilter) {
            $query->where('product_folder', $productFilter);
        } else {
            $query->whereIn('product_folder', [
                'LiveChat [Web]',
                'LiveChat [Mobile]',
                'Chatbot',
                'Email',
                'Chatbot AI',
                'Call Bot'
            ]);
        }

        if ($typeFilter) {
            $query->where('activity_type', $typeFilter);
        }

        return $query->orderBy('activity_date', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();
    }
}
