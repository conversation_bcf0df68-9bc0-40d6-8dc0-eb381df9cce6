<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ClickUpApiService
{
    private string $apiToken;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiToken = config('services.clickup.token');
        $this->baseUrl = config('services.clickup.base_url');
    }

    /**
     * Get all teams for the authenticated user
     */
    public function getTeams(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . '/team');

            if ($response->successful()) {
                return $response->json()['teams'] ?? [];
            }

            Log::error('Failed to fetch teams from ClickUp', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching teams from ClickUp', [
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get spaces for a specific team
     */
    public function getSpaces(string $teamId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/team/{$teamId}/space");

            if ($response->successful()) {
                return $response->json()['spaces'] ?? [];
            }

            Log::error('Failed to fetch spaces from ClickUp', [
                'team_id' => $teamId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching spaces from ClickUp', [
                'team_id' => $teamId,
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get folders for a specific space
     */
    public function getFolders(string $spaceId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/space/{$spaceId}/folder");

            if ($response->successful()) {
                return $response->json()['folders'] ?? [];
            }

            Log::error('Failed to fetch folders from ClickUp', [
                'space_id' => $spaceId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching folders from ClickUp', [
                'space_id' => $spaceId,
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get lists for a specific folder
     */
    public function getLists(string $folderId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/folder/{$folderId}/list");

            if ($response->successful()) {
                return $response->json()['lists'] ?? [];
            }

            Log::error('Failed to fetch lists from ClickUp', [
                'folder_id' => $folderId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching lists from ClickUp', [
                'folder_id' => $folderId,
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get folderless lists for a specific space
     */
    public function getFolderlessLists(string $spaceId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/space/{$spaceId}/list");

            if ($response->successful()) {
                return $response->json()['lists'] ?? [];
            }

            Log::error('Failed to fetch folderless lists from ClickUp', [
                'space_id' => $spaceId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching folderless lists from ClickUp', [
                'space_id' => $spaceId,
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get tasks for a specific list
     */
    public function getTasks(string $listId, array $params = []): array
    {
        try {
            $defaultParams = [
                'archived' => 'false',
                'include_closed' => 'true',
                'page' => 0,
            ];

            $queryParams = array_merge($defaultParams, $params);

            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/list/{$listId}/task", $queryParams);

            if ($response->successful()) {
                return $response->json()['tasks'] ?? [];
            }

            Log::error('Failed to fetch tasks from ClickUp', [
                'list_id' => $listId,
                'params' => $queryParams,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('Exception while fetching tasks from ClickUp', [
                'list_id' => $listId,
                'message' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get a specific task by ID
     */
    public function getTask(string $taskId): ?array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => $this->apiToken,
                'Accept' => 'application/json',
            ])->get($this->baseUrl . "/task/{$taskId}");

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Failed to fetch task from ClickUp', [
                'task_id' => $taskId,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while fetching task from ClickUp', [
                'task_id' => $taskId,
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }
}
