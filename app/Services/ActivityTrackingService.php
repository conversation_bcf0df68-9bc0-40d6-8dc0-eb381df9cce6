<?php

namespace App\Services;

use App\Models\ActivityLog;
use App\Models\ClickupTask;
use App\Models\ClickupList;
use App\Models\ClickupFolder;
use Carbon\Carbon;

class ActivityTrackingService
{
    // Define the 6 target product folders
    private $targetProducts = [
        'LiveChat [Web]',
        'LiveChat [Mobile]',
        'Chatbot',
        'Email',
        'Chatbot AI',
        'Call Bot'
    ];

    /**
     * Track task activities by comparing current state with previous state
     */
    public function trackTaskActivities(): void
    {
        $taqnyatTasks = ClickupTask::whereHas('folder', function($query) {
            $query->whereIn('name', $this->targetProducts);
        })->with(['folder'])->get();

        foreach ($taqnyatTasks as $task) {
            $this->analyzeTaskActivity($task);
        }
    }

    /**
     * Analyze individual task for activity
     */
    private function analyzeTaskActivity(ClickupTask $task): void
    {
        $productFolder = $task->folder->name ?? 'Unknown';
        
        // Check if task was recently created (within last sync period)
        if ($task->date_created && $task->date_created->gte(Carbon::now()->subHours(1))) {
            ActivityLog::logActivity([
                'type' => 'task_created',
                'entity_type' => 'task',
                'entity_id' => $task->clickup_id,
                'entity_name' => $task->name,
                'product_folder' => $productFolder,
                'user_name' => $task->assignees[0]['username'] ?? null,
                'user_id' => $task->assignees[0]['id'] ?? null,
                'description' => "Task '{$task->name}' was created in {$productFolder}",
                'activity_date' => $task->date_created,
            ]);
        }

        // Check if task was recently updated
        if ($task->date_updated && $task->date_updated->gte(Carbon::now()->subHours(1))) {
            ActivityLog::logActivity([
                'type' => 'task_updated',
                'entity_type' => 'task',
                'entity_id' => $task->clickup_id,
                'entity_name' => $task->name,
                'product_folder' => $productFolder,
                'user_name' => $task->assignees[0]['username'] ?? null,
                'user_id' => $task->assignees[0]['id'] ?? null,
                'description' => "Task '{$task->name}' was updated in {$productFolder}",
                'activity_date' => $task->date_updated,
            ]);
        }

        // Check if task was recently completed
        if ($task->date_done && $task->date_done->gte(Carbon::now()->subHours(1))) {
            ActivityLog::logActivity([
                'type' => 'task_completed',
                'entity_type' => 'task',
                'entity_id' => $task->clickup_id,
                'entity_name' => $task->name,
                'product_folder' => $productFolder,
                'user_name' => $task->assignees[0]['username'] ?? null,
                'user_id' => $task->assignees[0]['id'] ?? null,
                'description' => "Task '{$task->name}' was completed in {$productFolder}",
                'activity_date' => $task->date_done,
            ]);
        }

        // Check for status changes
        $currentStatus = $task->status['status'] ?? 'unknown';
        $this->trackStatusChange($task, $currentStatus, $productFolder);
    }

    /**
     * Track status changes
     */
    private function trackStatusChange(ClickupTask $task, string $currentStatus, string $productFolder): void
    {
        // For now, we'll log current status. In a real implementation, 
        // you'd compare with previous status stored in database
        if (in_array($currentStatus, ['in progress', 'in review', 'testing'])) {
            ActivityLog::logActivity([
                'type' => 'status_changed',
                'entity_type' => 'task',
                'entity_id' => $task->clickup_id,
                'entity_name' => $task->name,
                'product_folder' => $productFolder,
                'user_name' => $task->assignees[0]['username'] ?? null,
                'user_id' => $task->assignees[0]['id'] ?? null,
                'changes' => [
                    'status' => $currentStatus,
                    'changed_at' => $task->date_updated?->toISOString(),
                ],
                'description' => "Task '{$task->name}' status changed to '{$currentStatus}' in {$productFolder}",
                'activity_date' => $task->date_updated ?? Carbon::now(),
            ]);
        }
    }

    /**
     * Generate activity summary for dashboard
     */
    public function getActivitySummary(int $days = 7): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $activities = ActivityLog::where('activity_date', '>=', $startDate)
            ->whereNotNull('product_folder')
            ->orderBy('activity_date', 'desc')
            ->get();

        $summary = [
            'total_activities' => $activities->count(),
            'by_type' => $activities->groupBy('activity_type')->map->count(),
            'by_product' => $activities->groupBy('product_folder')->map->count(),
            'recent_activities' => $activities->take(10),
            'most_active_products' => $activities->groupBy('product_folder')
                ->map->count()
                ->sortDesc()
                ->take(3),
        ];

        return $summary;
    }

    /**
     * Get detailed activity timeline
     */
    public function getActivityTimeline(string $productFolder = null, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        $query = ActivityLog::query();
        
        if ($productFolder) {
            $query->where('product_folder', $productFolder);
        } else {
            $query->whereIn('product_folder', $this->targetProducts);
        }
        
        return $query->orderBy('activity_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean old activity logs (keep last 90 days)
     */
    public function cleanOldActivities(): int
    {
        $cutoffDate = Carbon::now()->subDays(90);
        
        return ActivityLog::where('activity_date', '<', $cutoffDate)->delete();
    }

    /**
     * Log sync activity
     */
    public function logSyncActivity(array $syncResults): void
    {
        ActivityLog::logActivity([
            'type' => 'data_sync',
            'entity_type' => 'system',
            'entity_id' => 'sync_' . time(),
            'entity_name' => 'ClickUp Data Sync',
            'product_folder' => 'System',
            'description' => "Data sync completed: {$syncResults['tasks']} tasks, {$syncResults['lists']} lists, {$syncResults['folders']} folders synced",
            'changes' => $syncResults,
            'activity_date' => Carbon::now(),
        ]);
    }
}
