<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClickupList extends Model
{
    protected $fillable = [
        'clickup_id',
        'name',
        'orderindex',
        'content',
        'status',
        'priority',
        'assignee',
        'task_count',
        'due_date',
        'start_date',
        'folder_id',
        'space_id',
        'archived',
        'last_synced_at',
    ];

    protected $casts = [
        'status' => 'array',
        'priority' => 'array',
        'assignee' => 'array',
        'due_date' => 'datetime',
        'start_date' => 'datetime',
        'archived' => 'boolean',
        'last_synced_at' => 'datetime',
    ];

    public function folder(): BelongsTo
    {
        return $this->belongsTo(ClickupFolder::class, 'folder_id');
    }

    public function space(): BelongsTo
    {
        return $this->belongsTo(ClickupSpace::class, 'space_id');
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(ClickupTask::class, 'list_id');
    }
}
