<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClickupTeam extends Model
{
    protected $fillable = [
        'clickup_id',
        'name',
        'color',
        'avatar',
        'members',
        'last_synced_at',
    ];

    protected $casts = [
        'members' => 'array',
        'last_synced_at' => 'datetime',
    ];

    public function spaces(): HasMany
    {
        return $this->hasMany(ClickupSpace::class, 'team_id');
    }
}
