<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClickupFolder extends Model
{
    protected $fillable = [
        'clickup_id',
        'name',
        'orderindex',
        'override_statuses',
        'hidden',
        'statuses',
        'space_id',
        'last_synced_at',
    ];

    protected $casts = [
        'override_statuses' => 'boolean',
        'hidden' => 'boolean',
        'statuses' => 'array',
        'last_synced_at' => 'datetime',
    ];

    public function space(): BelongsTo
    {
        return $this->belongsTo(ClickupSpace::class, 'space_id');
    }

    public function lists(): Has<PERSON>any
    {
        return $this->hasMany(ClickupList::class, 'folder_id');
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(ClickupTask::class, 'folder_id');
    }
}
