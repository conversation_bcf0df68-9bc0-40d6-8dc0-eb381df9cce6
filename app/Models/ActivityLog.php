<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ActivityLog extends Model
{
    protected $fillable = [
        'activity_type',
        'entity_type',
        'entity_id',
        'entity_name',
        'product_folder',
        'user_name',
        'user_id',
        'changes',
        'description',
        'activity_date',
    ];

    protected $casts = [
        'changes' => 'array',
        'activity_date' => 'datetime',
    ];

    /**
     * Create an activity log entry
     */
    public static function logActivity(array $data): self
    {
        return self::create([
            'activity_type' => $data['type'],
            'entity_type' => $data['entity_type'],
            'entity_id' => $data['entity_id'],
            'entity_name' => $data['entity_name'],
            'product_folder' => $data['product_folder'] ?? null,
            'user_name' => $data['user_name'] ?? null,
            'user_id' => $data['user_id'] ?? null,
            'changes' => $data['changes'] ?? null,
            'description' => $data['description'] ?? null,
            'activity_date' => $data['activity_date'] ?? Carbon::now(),
        ]);
    }

    /**
     * Get recent activities for Taqnyat products
     */
    public static function getRecentActivities(int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return self::whereNotNull('product_folder')
            ->orderBy('activity_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get activities by product folder
     */
    public static function getActivitiesByProduct(string $productFolder, int $limit = 20): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('product_folder', $productFolder)
            ->orderBy('activity_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get activity statistics
     */
    public static function getActivityStats(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);

        return [
            'total_activities' => self::where('activity_date', '>=', $startDate)->count(),
            'by_type' => self::where('activity_date', '>=', $startDate)
                ->selectRaw('activity_type, COUNT(*) as count')
                ->groupBy('activity_type')
                ->pluck('count', 'activity_type')
                ->toArray(),
            'by_product' => self::where('activity_date', '>=', $startDate)
                ->whereNotNull('product_folder')
                ->selectRaw('product_folder, COUNT(*) as count')
                ->groupBy('product_folder')
                ->pluck('count', 'product_folder')
                ->toArray(),
        ];
    }
}
