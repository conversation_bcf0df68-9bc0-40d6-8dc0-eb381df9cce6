<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClickupSpace extends Model
{
    protected $fillable = [
        'clickup_id',
        'name',
        'color',
        'private',
        'avatar',
        'statuses',
        'features',
        'team_id',
        'last_synced_at',
    ];

    protected $casts = [
        'private' => 'boolean',
        'avatar' => 'array',
        'statuses' => 'array',
        'features' => 'array',
        'last_synced_at' => 'datetime',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(ClickupTeam::class, 'team_id');
    }

    public function folders(): HasMany
    {
        return $this->hasMany(ClickupFolder::class, 'space_id');
    }

    public function lists(): HasMany
    {
        return $this->hasMany(ClickupList::class, 'space_id');
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(ClickupTask::class, 'space_id');
    }
}
