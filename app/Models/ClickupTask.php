<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClickupTask extends Model
{
    protected $fillable = [
        'clickup_id',
        'name',
        'description',
        'status',
        'orderindex',
        'date_created',
        'date_updated',
        'date_closed',
        'date_done',
        'assignees',
        'watchers',
        'checklists',
        'tags',
        'parent',
        'priority',
        'due_date',
        'start_date',
        'points',
        'time_estimate',
        'time_spent',
        'custom_fields',
        'dependencies',
        'linked_tasks',
        'team_id',
        'url',
        'permission_level',
        'list_id',
        'folder_id',
        'space_id',
        'last_synced_at',
    ];

    protected $casts = [
        'status' => 'array',
        'date_created' => 'datetime',
        'date_updated' => 'datetime',
        'date_closed' => 'datetime',
        'date_done' => 'datetime',
        'assignees' => 'array',
        'watchers' => 'array',
        'checklists' => 'array',
        'tags' => 'array',
        'priority' => 'array',
        'due_date' => 'datetime',
        'start_date' => 'datetime',
        'points' => 'array',
        'custom_fields' => 'array',
        'dependencies' => 'array',
        'linked_tasks' => 'array',
        'last_synced_at' => 'datetime',
    ];

    public function list(): BelongsTo
    {
        return $this->belongsTo(ClickupList::class, 'list_id');
    }

    public function folder(): BelongsTo
    {
        return $this->belongsTo(ClickupFolder::class, 'folder_id');
    }

    public function space(): BelongsTo
    {
        return $this->belongsTo(ClickupSpace::class, 'space_id');
    }
}
