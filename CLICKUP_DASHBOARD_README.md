# ClickUp Dashboard

A Laravel application for syncing and analyzing ClickUp data with team performance reports and insights.

## Features

- **Data Synchronization**: Sync teams, spaces, folders, lists, and tasks from ClickUp API
- **Dashboard**: Overview with statistics and charts
- **Team Management**: View team performance and completion rates
- **Task Management**: Filter and search tasks with advanced filtering
- **Reports & Analytics**: Performance insights and data visualization
- **Responsive Design**: Built with Tailwind CSS JIT CDN and Font Awesome

## Technology Stack

- **Backend**: Laravel 12 with SQLite database
- **Frontend**: Tailwind CSS JIT CDN, Font Awesome CDN, Chart.js, Alpine.js
- **API Integration**: ClickUp API v2
- **Database**: SQLite for local data storage

## Installation

1. The application is already set up and configured
2. ClickUp API token is configured in `.env`
3. Database migrations have been run
4. Data has been synced from ClickUp (825 tasks imported)

## Usage

### Accessing the Application

The application is available at: http://controls.test

### Syncing Data

Use the sync command to import data from ClickUp:

```bash
php artisan clickup:sync
```

Or use the "Sync Data" button in the web interface.

### Dashboard

The dashboard provides:
- Overview statistics (teams, spaces, lists, tasks)
- Task status distribution chart
- Task priority distribution chart
- Recent tasks timeline

### Teams Page

View all teams with:
- Team completion rates
- Progress bars
- Associated spaces and their statistics

### Tasks Page

Advanced task management with:
- Search functionality
- Filter by team, status, priority
- Pagination
- Direct links to ClickUp tasks

## API Integration

The application integrates with ClickUp API v2 to fetch:

1. **Teams**: `GET /team`
2. **Spaces**: `GET /team/{team_id}/space`
3. **Folders**: `GET /space/{space_id}/folder`
4. **Lists**: `GET /folder/{folder_id}/list` and `GET /space/{space_id}/list`
5. **Tasks**: `GET /list/{list_id}/task`

## Database Schema

The application uses the following main tables:

- `clickup_teams`: Store team information
- `clickup_spaces`: Store space information with team relationships
- `clickup_folders`: Store folder information with space relationships
- `clickup_lists`: Store list information with folder/space relationships
- `clickup_tasks`: Store task information with full details and relationships

## Current Data Status

✅ **Successfully Synced:**
- 3 Teams
- 3 Spaces
- 11 Folders
- 40 Lists
- 825 Tasks

## Commands

- `php artisan clickup:sync`: Sync all data from ClickUp
- `php artisan clickup:sync --full`: Perform a full sync (same as above)

## Features Implemented

✅ **Core Features:**
- Laravel application with SQLite database
- ClickUp API integration service
- Data synchronization command
- Responsive layout with Tailwind CSS JIT CDN
- Font Awesome CDN integration
- Dashboard with statistics and charts
- Team management page
- Task management with filtering
- Data visualization with Chart.js

✅ **Technical Implementation:**
- Eloquent models with relationships
- Database migrations
- API service class
- Artisan commands
- Controllers and routes
- Blade templates
- Error handling and logging

## Next Steps for Enhancement

The application is fully functional and ready for use. Potential enhancements could include:

1. **Advanced Reports**: More detailed analytics and performance metrics
2. **User Authentication**: Multi-user support with role-based access
3. **Real-time Updates**: WebSocket integration for live data updates
4. **Export Features**: PDF/Excel export of reports
5. **Notifications**: Email/Slack notifications for task updates
6. **Custom Fields**: Support for ClickUp custom fields in reports

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
