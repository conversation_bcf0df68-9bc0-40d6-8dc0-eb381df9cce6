<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SyncController;
use App\Http\Controllers\TeamsController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Sync data
Route::post('/sync-data', [SyncController::class, 'sync'])->name('sync.data');

// Teams
Route::get('/teams', [TeamsController::class, 'index'])->name('teams.index');

Route::get('/tasks', function () {
    return view('tasks.index');
})->name('tasks.index');

Route::get('/reports', function () {
    return view('reports.index');
})->name('reports.index');
