<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SyncController;
use App\Http\Controllers\TeamsController;
use App\Http\Controllers\TasksController;
use App\Http\Controllers\TaqnyatDashboardController;
use App\Http\Controllers\TaqnyatReportsController;
use App\Http\Controllers\ActivityController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Sync data
Route::post('/sync-data', [SyncController::class, 'sync'])->name('sync.data');

// Teams
Route::get('/teams', [TeamsController::class, 'index'])->name('teams.index');

Route::get('/tasks', [TasksController::class, 'index'])->name('tasks.index');

// Taqnyat-focused reports (replaces general reports)
Route::get('/reports', [TaqnyatReportsController::class, 'index'])->name('reports.index');
Route::get('/reports/tasks', [TaqnyatReportsController::class, 'taskDetails'])->name('taqnyat.reports.tasks');
Route::get('/reports/export', [TaqnyatReportsController::class, 'exportTasks'])->name('taqnyat.reports.export');

// Taqnyat Product Management Dashboard
Route::get('/taqnyat', [TaqnyatDashboardController::class, 'index'])->name('taqnyat.dashboard');
Route::get('/taqnyat/product/{folder}', [TaqnyatDashboardController::class, 'productDetail'])->name('taqnyat.product-detail');

// Additional Taqnyat Reports Routes
Route::get('/taqnyat/reports', [TaqnyatReportsController::class, 'index'])->name('taqnyat.reports');
Route::get('/taqnyat/reports/tasks', [TaqnyatReportsController::class, 'taskDetails'])->name('taqnyat.reports.tasks');
Route::get('/taqnyat/reports/export', [TaqnyatReportsController::class, 'exportTasks'])->name('taqnyat.reports.export');

// Activity Timeline Routes
Route::get('/activity', [ActivityController::class, 'index'])->name('activity.index');
Route::get('/activity/api', [ActivityController::class, 'getActivities'])->name('activity.api');
