<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SyncController;
use App\Http\Controllers\TeamsController;
use App\Http\Controllers\TasksController;
use App\Http\Controllers\TaqnyatDashboardController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Sync data
Route::post('/sync-data', [SyncController::class, 'sync'])->name('sync.data');

// Teams
Route::get('/teams', [TeamsController::class, 'index'])->name('teams.index');

Route::get('/tasks', [TasksController::class, 'index'])->name('tasks.index');

Route::get('/reports', function () {
    return view('reports.index');
})->name('reports.index');

// Taqnyat Product Management Dashboard
Route::get('/taqnyat', [TaqnyatDashboardController::class, 'index'])->name('taqnyat.dashboard');
Route::get('/taqnyat/product/{folder}', [TaqnyatDashboardController::class, 'productDetail'])->name('taqnyat.product-detail');
