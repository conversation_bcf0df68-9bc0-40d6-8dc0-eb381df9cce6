<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_folders', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->string('name');
            $table->integer('orderindex')->nullable();
            $table->boolean('override_statuses')->default(false);
            $table->boolean('hidden')->default(false);
            $table->json('statuses')->nullable();
            $table->foreignId('space_id')->constrained('clickup_spaces')->onDelete('cascade');
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_folders');
    }
};
