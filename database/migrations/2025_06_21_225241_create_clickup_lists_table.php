<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_lists', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->string('name');
            $table->integer('orderindex')->nullable();
            $table->text('content')->nullable();
            $table->json('status')->nullable();
            $table->json('priority')->nullable();
            $table->json('assignee')->nullable();
            $table->integer('task_count')->default(0);
            $table->timestamp('due_date')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->foreignId('folder_id')->nullable()->constrained('clickup_folders')->onDelete('cascade');
            $table->foreignId('space_id')->constrained('clickup_spaces')->onDelete('cascade');
            $table->boolean('archived')->default(false);
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_lists');
    }
};
