<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->string('activity_type'); // created, updated, completed, etc.
            $table->string('entity_type'); // task, list, folder
            $table->string('entity_id'); // ClickUp ID of the entity
            $table->string('entity_name'); // Name of the entity
            $table->string('product_folder')->nullable(); // Which product folder
            $table->string('user_name')->nullable(); // Who performed the action
            $table->string('user_id')->nullable(); // ClickUp user ID
            $table->json('changes')->nullable(); // What changed (before/after)
            $table->text('description')->nullable(); // Human readable description
            $table->timestamp('activity_date'); // When the activity occurred
            $table->timestamps();

            // Indexes for better performance
            $table->index(['activity_type', 'activity_date']);
            $table->index(['entity_type', 'entity_id']);
            $table->index(['product_folder', 'activity_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
