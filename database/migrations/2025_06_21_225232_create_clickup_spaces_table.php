<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_spaces', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->string('name');
            $table->text('color')->nullable();
            $table->boolean('private')->default(false);
            $table->json('avatar')->nullable();
            $table->json('statuses')->nullable();
            $table->json('features')->nullable();
            $table->foreignId('team_id')->constrained('clickup_teams')->onDelete('cascade');
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_spaces');
    }
};
