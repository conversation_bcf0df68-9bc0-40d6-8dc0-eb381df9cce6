<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clickup_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('clickup_id')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('status');
            $table->integer('orderindex')->nullable();
            $table->timestamp('date_created')->nullable();
            $table->timestamp('date_updated')->nullable();
            $table->timestamp('date_closed')->nullable();
            $table->timestamp('date_done')->nullable();
            $table->json('assignees')->nullable();
            $table->json('watchers')->nullable();
            $table->json('checklists')->nullable();
            $table->json('tags')->nullable();
            $table->string('parent')->nullable();
            $table->json('priority')->nullable();
            $table->timestamp('due_date')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->json('points')->nullable();
            $table->integer('time_estimate')->nullable();
            $table->integer('time_spent')->nullable();
            $table->json('custom_fields')->nullable();
            $table->json('dependencies')->nullable();
            $table->json('linked_tasks')->nullable();
            $table->string('team_id')->nullable();
            $table->string('url')->nullable();
            $table->string('permission_level')->nullable();
            $table->foreignId('list_id')->constrained('clickup_lists')->onDelete('cascade');
            $table->foreignId('folder_id')->nullable()->constrained('clickup_folders')->onDelete('cascade');
            $table->foreignId('space_id')->constrained('clickup_spaces')->onDelete('cascade');
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clickup_tasks');
    }
};
