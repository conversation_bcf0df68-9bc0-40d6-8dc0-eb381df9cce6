
# project main 
create app using laravel and tailwind JIT cdn and font awsome cdn and sqlite 

the app responsable to gather data from diffrent sources and import them to local database and make some opration to make some reports and insights 


## source 1 : click up 


API Version 2 Token is 
pk_707692_PMLY04LLG62VA71M64IP00KFAIGUV2F9

1. we need to save the team id 

curl --request GET \
     --url https://api.clickup.com/api/v2/team \
     --header 'Authorization: pk_707692_PMLY04LLG62VA71M64IP00KFAIGUV2F9' \
     --header 'accept: application/json'

2. then space id 

curl --request GET \
     --url https://api.clickup.com/api/v2/team/{team_id}/space \
     --header 'Authorization: pk_707692_PMLY04LLG62VA71M64IP00KFAIGUV2F9' \
     --header 'accept: application/json'

3. then folder id 

curl --request GET \
     --url https://api.clickup.com/api/v2/space/{space_id}/folder \
     --header 'Authorization: pk_707692_PMLY04LLG62VA71M64IP00KFAIGUV2F9' \
     --header 'accept: application/json'

4. then list id 

curl --request GET \
     --url https://api.clickup.com/api/v2/folder/{folder_id}/list \
     --header 'Authorization: pk_707692_PMLY04LLG62VA71M64IP00KFAIGUV2F9' \
     --header 'accept: application/json'

the point i want to sync the tasks in all folders to create a report for the performance of the team 

## source 2 : 360dialog 

## source 3 : Taqnyat Admin 

## source 4 : emails

## source 5 : etimad
https://tenders.etimad.sa/Tender/AllTendersForVisitor?PageNumber=2
# others

use google ai 
https://aistudio.google.com/app/apikey

Goal 
Performace


Products [Bugs , Features , Docs , pages , Research]
Projects 
    -> Tenders History
    -> workshop with sales team 
    -> product team traing to support sales
    -> Credit line Rama
    -> FB Verify to all accounts 
    -> closed account reports 
    -> CRM new leads source and closed loss
    -> Ghaleb 1000 lead follow up 
    -> Ghaleb VIP Client follow up Lama
    -> Ghaleb VIP Client follow up yassen
    -> Big new clients
    -> Integrations with salesforace , odoo , shopify , intercome sdk  
    -> front end and quility hirring


Anoucements 
    -> Ticket and roles support and products
    -> 